# CLAUDE.md - <PERSON><PERSON> App UI 移动应用

## 项目概述
**<PERSON><PERSON> (赛点联盟)** - 基于 uni-app + Vue3 开发的篮球运动平台移动应用，支持微信小程序、H5、App 多端部署。

## 快速开始
```bash
# 安装依赖
pnpm install

# 代码格式化
pnpm prettier

# 开发环境
# 使用 HBuilderX 或 uni-app CLI 工具进行开发和调试
# 支持平台：微信小程序、H5、App（iOS/Android）
```

## 核心技术栈
- **uni-app** - 跨平台开发框架
- **Vue 3** - 前端框架
- **Pinia** - 状态管理
- **SCSS** - 样式预处理器
- **dayjs** - 日期处理
- **lodash** - 工具库

## 项目结构
```
saidian-app-ui/
├── pages/                    # 页面目录
│   ├── index/               # 首页模块
│   │   ├── components/      # 首页组件
│   │   ├── index.vue        # 首页
│   │   ├── game.vue         # 比赛页面
│   │   ├── user.vue         # 个人中心
│   │   └── player-career.vue # 球员生涯
│   ├── activity/            # 活动模块
│   ├── game/                # 比赛模块
│   ├── team/                # 球队模块
│   ├── player/              # 球员模块
│   ├── order/               # 订单模块
│   └── pay/                 # 支付模块
├── sheep/                   # 核心框架
│   ├── api/                 # API 接口
│   │   ├── operation/       # 业务 API
│   │   ├── member/          # 会员 API
│   │   ├── pay/            # 支付 API
│   │   └── system/         # 系统 API
│   ├── components/          # 公共组件
│   ├── store/              # 状态管理
│   ├── hooks/              # 组合式函数
│   ├── router/             # 路由管理
│   ├── config/             # 配置文件
│   └── util/               # 工具函数
├── static/                  # 静态资源
├── uni_modules/            # uni-app 插件
└── ui/                     # 设计系统
```

## 核心业务模块

### 1. 首页模块 (pages/index)
**关键页面**: `index.vue`, `game.vue`, `player-career.vue`, `team-home.vue`
- **首页**: 展示推荐活动、比赛赛程、球员排行
- **比赛页面**: 比赛列表、赛程安排、结果统计
- **球员生涯**: 球员个人信息、生涯数据、技能展示
- **球队主页**: 球队信息、成员管理、战绩统计

### 2. 活动模块 (pages/activity)
**关键页面**: `list.vue`, `detail.vue`, `confirm-order.vue`
- **活动列表**: 日常活动、联赛活动展示
- **活动详情**: 活动信息、报名状态、费用说明
- **确认订单**: 报名费用结算、支付流程

### 3. 比赛模块 (pages/game)
**关键页面**: `game-detail.vue`, `game-result.vue`, `register/`
- **比赛详情**: 比赛信息、参赛队伍、数据统计
- **比赛结果**: 比赛成绩、球员表现、数据分析
- **比赛报名**: 联赛报名、排位赛报名

### 4. 球队模块 (pages/team)
**关键页面**: `team-info.vue`, `create-team.vue`, `player-market.vue`
- **球队信息**: 球队详情、成员列表、战绩统计
- **创建球队**: 球队成立、设置管理
- **球员市场**: 球员交易、合同管理

### 5. 球员模块 (pages/player)
**关键页面**: `player-career-other.vue`, `player-edit.vue`, `player-ranking-list.vue`
- **球员信息**: 个人资料、生涯数据、技能展示
- **球员编辑**: 信息修改、技能提升
- **球员排行**: 能力排行、数据统计

## API 接口架构

### 业务 API (sheep/api/operation/)
- **activity.js** - 活动相关接口
- **player.js** - 球员相关接口
- **team.js** - 球队相关接口
- **game.js** - 比赛相关接口
- **league.js** - 联赛相关接口

### 核心 API 示例
```javascript
// 活动相关 API
export default {
  // 获取活动列表
  getActivityPage: (params) => request({
    url: '/operation/activity/page',
    method: 'GET',
    params,
  }),
  
  // 获取活动详情
  getActivityDetail: (id) => request({
    url: `/operation/activity/detail/${id}`,
    method: 'GET',
  }),
  
  // 创建报名
  createRegistration: (data) => request({
    url: '/operation/registration/create',
    method: 'POST',
    data,
  }),
}

// 球员相关 API
export default {
  // 获取球员市场
  getPlayerMarket: (params) => request({
    url: '/league/player/market/page',
    method: 'GET',
    params,
  }),
  
  // 更新球员信息
  updatePlayer: (data) => request({
    url: '/league/player/update',
    method: 'PUT',
    data,
  }),
}
```

## 状态管理 (Pinia)

### 核心 Store
- **app.js** - 应用全局状态
- **user.js** - 用户信息状态
- **activity.js** - 活动状态管理
- **cart.js** - 购物车状态

### Store 使用示例
```javascript
// user.js
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLogin: false,
  }),
  
  actions: {
    // 设置用户信息
    setUserInfo(info) {
      this.userInfo = info;
      this.isLogin = true;
    },
    
    // 清除用户信息
    clearUserInfo() {
      this.userInfo = null;
      this.isLogin = false;
    },
  },
});
```

## 组件系统

### 公共组件 (sheep/components/)
- **s-activity-list** - 活动列表组件
- **s-game-card** - 比赛卡片组件
- **s-player-info** - 球员信息组件
- **s-team-stats** - 球队统计组件
- **s-custom-navbar** - 自定义导航栏

### 组件使用示例
```vue
<template>
  <view class="game-container">
    <!-- 比赛卡片组件 -->
    <game-card 
      v-for="game in games" 
      :key="game.id" 
      :game="game"
      @tap="toGameDetail(game)"
    />
    
    <!-- 球员信息组件 -->
    <player-info 
      :player="currentPlayer" 
      :show-stats="true"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import GameCard from '@/sheep/components/game-card.vue';
import PlayerInfo from '@/sheep/components/player-info.vue';

const games = ref([]);
const currentPlayer = ref(null);
</script>
```

## 样式系统

### SCSS 架构
```scss
// sheep/scss/
├── _main.scss          // 主样式文件
├── _var.scss           // 变量定义
├── _mixins.scss        // 混合器
├── _tools.scss         // 工具类
├── theme/             // 主题样式
│   ├── _light.scss     // 亮色主题
│   └── _dark.scss      // 暗色主题
└── style/              // 组件样式
    ├── _button.scss    // 按钮样式
    ├── _card.scss      // 卡片样式
    └── _form.scss      // 表单样式
```

### 样式变量示例
```scss
// sheep/scss/_var.scss
// 主题颜色
$primary-color: #4b8fe1;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;

// 布局
$spacing-unit: 8px;
$border-radius: 8px;

// 字体
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-sm: 12px;
```

## 路由配置

### 页面路由 (pages.json)
```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true
      },
      "meta": {
        "auth": false,
        "title": "首页",
        "group": "商城"
      }
    }
  ],
  "tabBar": {
    "list": [
      {"pagePath": "pages/index/index"},
      {"pagePath": "pages/index/game"},
      {"pagePath": "pages/index/team-home"},
      {"pagePath": "pages/index/player-career"},
      {"pagePath": "pages/index/user"}
    ]
  }
}
```

## 工具函数

### 常用工具 (sheep/util/)
- **index.js** - 通用工具函数
- **daily.js** - 日常活动工具
- **const.js** - 常量定义

### 工具函数示例
```javascript
// sheep/util/index.js
// 格式化时间
export function formatTime(time) {
  return dayjs(time).format('YYYY-MM-DD HH:mm');
}

// 计算年龄
export function calculateAge(birthdate) {
  return dayjs().diff(dayjs(birthdate), 'year');
}

// 防抖函数
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
```

## 开发规范

### 1. 代码规范
- **组件命名**: 使用 kebab-case 命名
- **文件命名**: 使用 kebab-case 命名
- **API 命名**: 使用 camelCase 命名
- **样式命名**: 使用 BEM 命名规范

### 2. 组件开发规范
```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
});

// 定义 emits
const emit = defineEmits(['click', 'change']);

// 响应式数据
const count = ref(0);

// 计算属性
const doubleCount = computed(() => count.value * 2);

// 方法
const handleClick = () => {
  emit('click', count.value);
};
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式
}
</style>
```

### 3. API 调用规范
```javascript
// 使用 try-catch 处理 API 错误
try {
  const result = await ActivityApi.getActivityDetail(id);
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('获取活动详情失败:', error);
  uni.showToast({
    title: '加载失败',
    icon: 'none'
  });
}
```

### 4. 状态管理规范
```javascript
// 使用 Pinia 进行状态管理
export const useActivityStore = defineStore('activity', {
  state: () => ({
    activities: [],
    currentActivity: null,
  }),
  
  getters: {
    // 获取推荐活动
    recommendedActivities: (state) => {
      return state.activities.filter(activity => activity.isRecommended);
    },
  },
  
  actions: {
    // 异步获取活动列表
    async fetchActivities(params) {
      try {
        const result = await ActivityApi.getActivityPage(params);
        this.activities = result.data;
      } catch (error) {
        console.error('获取活动列表失败:', error);
      }
    },
  },
});
```

## 性能优化

### 1. 图片优化
- 使用合适的图片格式（WebP、JPEG）
- 压缩图片大小
- 使用懒加载

### 2. 列表优化
- 使用虚拟滚动
- 分页加载
- 图片懒加载

### 3. 缓存策略
- 使用 Pinia 持久化存储
- 缓存 API 响应数据
- 使用本地存储

## 多端适配

### 1. 平台差异处理
```javascript
// sheep/platform/index.js
// 根据不同平台进行适配
export function getPlatformConfig() {
  // #ifdef H5
  return { platform: 'h5' };
  // #endif
  
  // #ifdef MP-WEIXIN
  return { platform: 'wechat' };
  // #endif
  
  // #ifdef APP-PLUS
  return { platform: 'app' };
  // #endif
}
```

### 2. 样式适配
```scss
// 使用 rpx 单位进行适配
.container {
  width: 750rpx; // 适配不同屏幕宽度
  padding: 20rpx;
}

// 平台特定样式
/* #ifdef H5 */
.container {
  max-width: 750rpx;
  margin: 0 auto;
}
/* #endif */
```

## 测试和调试

### 1. 开发调试
- 使用 HBuilderX 调试工具
- 浏览器开发者工具（H5）
- 微信开发者工具（小程序）

### 2. 错误处理
```javascript
// 全局错误处理
uni.onError((error) => {
  console.error('应用错误:', error);
  
  // 上报错误信息
  reportError(error);
});

// 网络错误处理
uni.onNetworkStatusChange((res) => {
  if (!res.isConnected) {
    uni.showToast({
      title: '网络连接已断开',
      icon: 'none'
    });
  }
});
```

## 部署发布

### 1. 微信小程序
```bash
# 在 HBuilderX 中发布
# 1. 运行 -> 运行到小程序模拟器 -> 微信开发者工具
# 2. 发行 -> 上传到微信小程序后台
```

### 2. H5 部署
```bash
# 在 HBuilderX 中发布
# 1. 发行 -> 网站-H5手机版
# 2. 将生成的文件部署到服务器
```

### 3. App 打包
```bash
# 在 HBuilderX 中发布
# 1. 发行 -> 原生App-云打包
# 2. 选择 Android 或 iOS 平台
# 3. 配置应用信息并打包
```

## 常见问题

### 1. 跨域问题（H5）
- 配置代理服务器
- 使用 JSONP 或 CORS

### 2. 性能问题
- 减少页面层级
- 优化图片资源
- 使用分包加载

### 3. 兼容性问题
- 测试不同平台
- 使用条件编译
- 处理平台差异

## 开发资源

### 相关链接
- **uni-app 官方文档**: https://uniapp.dcloud.io/
- **Vue 3 官方文档**: https://vuejs.org/
- **Pinia 官方文档**: https://pinia.vuejs.org/
- **后端 API 文档**: http://localhost:58080/doc.html

### 开发工具
- **HBuilderX**: 官方 IDE
- **微信开发者工具**: 小程序调试
- **Chrome DevTools**: H5 调试
- **Git**: 版本控制

### 参考文档
- **uni-app 组件文档**: https://uniapp.dcloud.io/component/
- **API 接口文档**: sheep/api/ 目录下各文件
- **设计系统**: ui/ 目录下设计文档