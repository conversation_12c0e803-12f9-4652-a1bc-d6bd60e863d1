# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Saidian (赛点联盟)** project - a comprehensive basketball sports platform system based on RuoYi-Vue-Pro. It manages basketball activities, leagues, players, teams, and statistics through three main components:

1. **saidian-admin-ui** - Vue3 + Element Plus admin dashboard for platform management
2. **saidian-app-ui** - uni-app mobile application (小程序/H5/App) for players and teams
3. **saidian-server** - Spring Boot backend server with modular architecture

## Development Guidelines

### Communication Principles
- 所有回答都使用中文。

## Development Commands

### Frontend (Admin UI)
```bash
# Navigate to admin UI
cd saidian-admin-ui

# Install dependencies (use pnpm only)
pnpm install

# Start development server
pnpm dev          # Local development (env.local)
pnpm dev-server   # Remote development (dev)

# Build for different environments
pnpm build:local  # Local build
pnpm build:dev    # Development build
pnpm build:test   # Test build
pnpm build:stage  # Stage build
pnpm build:prod   # Production build

# Linting and formatting
pnpm lint:eslint  # ESLint check and fix
pnpm lint:format  # Prettier formatting
pnpm lint:style   # Stylelint check

# Testing
pnpm test         # Run tests with Vitest
pnpm coverage     # Test coverage

# Type checking
pnpm ts:check     # TypeScript check

# Other utilities
pnpm preview      # Preview build result
pnpm clean        # Clean node_modules
pnpm clean:cache  # Clean cache
```

### Mobile App (uni-app)
```bash
# Navigate to app UI
cd saidian-app-ui

# Install dependencies
pnpm install

# Code formatting
pnpm prettier     # Format code with Prettier

# Development uses HBuilderX or uni-app CLI tools
# Build targets: WeChat Mini Program, H5, App (iOS/Android)
```

### Backend (Spring Boot)
```bash
# Navigate to server
cd saidian-server

# Clean and compile
mvn clean compile

# Run all tests
mvn test

# Run tests for specific modules
mvn test -pl :saidian-module-operation -am    # Sports operations module
mvn test -pl :yudao-module-system -am        # System module
mvn test -pl :yudao-module-pay -am           # Payment module

# Build project
mvn clean package

# Run application (after building)
java -jar yudao-server/target/yudao-server.jar

# Run with specific profile
java -jar yudao-server/target/yudao-server.jar --spring.profiles.active=local  # Local development
java -jar yudao-server/target/yudao-server.jar --spring.profiles.active=dev    # Development
java -jar yudao-server/target/yudao-server.jar --spring.profiles.active=test   # Testing
java -jar yudao-server/target/yudao-server.jar --spring.profiles.active=prod  # Production

# Access management interfaces
# Application runs on port 58080 by default
# Swagger UI: http://localhost:58080/swagger-ui
# Knife4j API docs: http://localhost:58080/doc.html
```

## Architecture & Code Structure

### Backend Architecture
- **Base Framework**: Spring Boot 2.7.18 with Java 8
- **Database**: MyBatis Plus 3.5.7 for ORM, MySQL 5.7/8.0+ supported
- **Security**: Spring Security 5.8.14 + JWT tokens + Redis sessions
- **Cache**: Redis 5.0+ with Redisson 3.36.0 client
- **Task Scheduling**: Quartz for scheduled jobs
- **Workflow**: Flowable 6.8.0 engine for BPM processes
- **Documentation**: Springdoc (OpenAPI 3) with Knife4j
- **Message Queue**: RocketMQ, Kafka, RabbitMQ support
- **Monitoring**: Spring Boot Admin, Actuator endpoints

### Module Structure
The backend follows a modular architecture pattern:

```
saidian-server/
├── yudao-framework/          # Core framework components
│   ├── yudao-common/         # Common utilities and constants
│   ├── yudao-spring-boot-starter-*/  # Various starters
├── yudao-server/            # Main application entry point
├── yudao-module-system/     # User/role/permission management
├── yudao-module-infra/      # Infrastructure (files, logs, etc.)
├── yudao-module-member/     # Member management
├── yudao-module-pay/        # Payment system integration
├── yudao-module-mall/       # E-commerce functionality
├── saidian-module-operation/ # Sports platform business logic
│   ├── saidian-module-operation-api/    # API interfaces and DTOs
│   └── saidian-module-operation-biz/   # Business implementation
└── yudao-dependencies/      # Dependency management (BOM)
```

### Core Business Module - saidian-module-operation
This is the heart of the Saidian platform containing:

**Activity Management**
- Basketball activities creation and management
- Daily activities and leagues
- Activity templates and scheduling
- Registration management and payment processing
- Automatic status management and refunds

**Player & Team Management**
- Player profiles and career statistics
- Team creation and management
- Player registration and assignment
- Contract management
- Performance analytics

**Game & Statistics**
- Game scheduling and result recording
- Real-time data entry during games
- Player and team statistics
- Career data tracking and analysis
- Radar charts and performance metrics

**League Operations**
- League creation from templates
- League registration and team management
- Game scheduling and standings
- Championship management

### Frontend Architecture
- **Admin UI**: Vue 3.5.12 + TypeScript 5.3.3 + Element Plus 2.9.1 + Vite 5.1.4
- **Mobile App**: uni-app framework for cross-platform deployment
- **State Management**: Pinia 2.1.7 with persistence
- **Styling**: UnoCSS atomic CSS framework
- **Icons**: Iconify icon library
- **Charts**: ECharts 5.5.0 for data visualization

### Database Configuration
- **Default Profile**: MySQL (saidian database)
- **Connection Pool**: Druid with monitoring
- **ORM**: MyBatis Plus with dynamic data source support
- **Redis**: Caching and session management
- **Multi-tenancy**: Supported but disabled by default

## Development Guidelines

### Code Quality Standards
1. **Minimal Changes**: Make the smallest possible changes to achieve the requirement
2. **Preserve Functionality**: When refactoring, never change existing business logic
3. **Type Safety**: Use TypeScript strictly in frontend code
4. **Consistent Patterns**: Follow existing code patterns and conventions
5. **Performance**: Consider performance implications for data-heavy operations

### Backend Patterns
- **Service Layer**: Business logic in `@Service` classes with clear interfaces
- **Data Access**: MyBatis Plus with `@Mapper` interfaces
- **DTOs**: Use separate DTOs for API communication vs internal objects
- **Error Handling**: Use consistent error codes and exceptions from framework
- **Validation**: JSR-303 validation annotations
- **Transactions**: Proper `@Transactional` boundaries
- **Async Processing**: Use `@Async` for long-running operations

### Frontend Patterns
- **Components**: Vue 3 Composition API with `<script setup>`
- **Type Definitions**: Define proper TypeScript interfaces
- **API Calls**: Centralized API service layer with error handling
- **Form Handling**: Element Plus form validation
- **State Management**: Pinia stores for complex state
- **Routing**: Vue Router 4 with proper guards

### Business Logic Patterns
- **Activity Lifecycle**: Status-based state management
- **Registration Flow**: Multi-step process with payment integration
- **Team Assignment**: Automatic balancing algorithms
- **Statistics**: Real-time calculation and historical tracking
- **Payment Processing**: Integration with multiple payment providers

## Environment Configuration

### Backend Profiles
- **local**: Local development (port 58080, MySQL on localhost)
- **dev**: Development environment
- **test**: Testing environment
- **prod**: Production environment

### Key Configuration Files
- `application.yaml` - Main configuration
- `application-local.yaml` - Local development settings
- `application-dev.yaml` - Development environment
- `application-test.yaml` - Test environment
- `application-prod.yaml` - Production environment

## Testing

### Backend Testing
- **Unit Tests**: JUnit 5 + Mockito
- **Integration Tests**: Spring Boot Test
- **Test Profiles**: Separate configuration for testing
- **Test Database**: H2 or separate MySQL schema

### Frontend Testing
- **Unit Tests**: Vitest with Vue Test Utils
- **Component Testing**: Vue component testing
- **E2E Testing**: Manual testing across platforms

### Key Test Commands
```bash
# Backend
mvn test                                    # Run all tests
mvn test -pl :saidian-module-operation-biz -q "-Dmaven.test.failure.ignore=false"   # Test operations module
mvn test -pl :saidian-module-operation -am   # Test operations module (alternative)

# Frontend
cd saidian-admin-ui && pnpm test             # Run admin UI tests
pnpm coverage                               # Test coverage
```

## Key Technical Decisions

### Technology Stack
- **Node.js**: Requires 16.18.0+ with pnpm 8.6.0+
- **Java**: JDK 8 (due to legacy requirements)
- **Database**: MySQL 5.7/8.0+ in production
- **Cache**: Redis 5.0+ required
- **Build**: Maven for backend, Vite for frontend

### Architecture Decisions
- **Modular Design**: Loose coupling between business modules
- **Event-Driven**: Message queues for async processing
- **Caching Strategy**: Redis for performance optimization
- **Security**: JWT-based authentication with role-based access
- **Monitoring**: Comprehensive logging and health checks

## Important Notes

### Framework Customization
- This is a customized version of RuoYi-Vue-Pro framework
- Extensive business logic specific to basketball sports management
- Multi-tenant SaaS architecture support (configurable)

### Business Complexity
- Complex activity registration and payment flows
- Real-time game statistics and data entry
- Sophisticated team assignment algorithms
- Comprehensive career statistics tracking
- Integration with multiple payment providers

### Development Resources
- Extensive documentation in `/docs` and `/logic` directories
- API documentation available through Swagger/Knife4j
- Business requirement documents in `saidian-reqirement/`
- Database schema in `saidian_tmp_schema.sql`

### Performance Considerations
- Heavy use of caching for statistics and player data
- Batch processing for large datasets
- Async processing for long-running operations
- Database optimization for frequent read operations