# 赛点联盟 - 球员生涯统计模块路由配置说明

## 概述

本文档说明如何在赛点联盟体育平台中配置球员生涯统计模块的路由。该平台使用动态路由系统，所有路由通过后端菜单配置生成。

## 路由配置原理

### 动态路由系统
- 前端使用 `@/router/modules/remaining.ts` 定义基础路由
- 业务路由通过后端 `system_menu` 表动态生成
- 菜单权限与路由权限统一管理

### 路由生成流程
1. 用户登录后获取权限信息
2. 后端根据用户角色返回菜单配置
3. 前端动态添加路由到路由表
4. 根据菜单配置生成侧边栏导航

## 球员生涯统计模块路由

### 已实现的功能模块

#### 1. 球员管理 (`/league/player`)
- **路径**: `/league/player`
- **组件**: `@/views/league/player/index.vue`
- **权限**: `league:player:*`
- **功能**: 球员列表、新增、编辑、删除、能力值更新、生涯数据管理

#### 2. 球员生涯统计 (`/league/player/career-stats`)
- **路径**: `/league/player/career-stats`
- **组件**: `@/views/league/player/career-stats/index.vue`
- **权限**: `league:player-career-stats:*`
- **功能**: 球员生涯数据查看、数据初始化、状态检查

#### 3. 球员对比分析 (`/league/player/comparison`)
- **路径**: `/league/player/comparison`
- **组件**: `@/views/league/player/comparison/index.vue`
- **权限**: `league:player-comparison:*`
- **功能**: 多球员数据对比、图表展示、雷达图分析

#### 4. 生涯统计排行榜 (`/league/player/career-leaderboard`)
- **路径**: `/league/player/career-leaderboard`
- **组件**: `@/views/league/player/career-leaderboard/index.vue`
- **权限**: `league:player-career-leaderboard:*`
- **功能**: 各项数据排行榜、赛季筛选、分页展示

## 菜单配置SQL

### 基础菜单结构
```sql
-- 联赛管理菜单
INSERT INTO system_menu (name, permission, type, icon, component, path, sort, status, parent_id) 
VALUES ('联赛管理', '', 1, 'ep:basketball', '', 2000, 0, 0);

-- 球员管理菜单
INSERT INTO system_menu (name, permission, type, icon, component, path, sort, status, parent_id) 
VALUES ('球员管理', 'league:player:*', 1, 'ep:user', '/league/player', 2010, 0, 2000);

-- 球员列表
INSERT INTO system_menu (name, permission, type, icon, component, path, sort, status, parent_id) 
VALUES ('球员列表', 'league:player:query', 2, 'ep:list', 'league/player/index', 2011, 0, 2010);
```

### 完整菜单配置
完整的菜单配置SQL文件位于：`saidian_tmp_schema.sql`

包含以下菜单项：
- 联赛管理 (一级菜单)
  - 球员管理 (二级菜单)
    - 球员列表 (三级菜单)
  - 生涯统计 (二级菜单)
    - 生涯数据 (三级菜单)
    - 球员对比 (三级菜单)
    - 数据排行 (三级菜单)

## 权限配置

### 权限编码规范
- 格式: `模块:功能:操作`
- 示例: `league:player:query` (联赛-球员-查询)

### 已配置权限
```sql
-- 球员管理权限
'league:player:create',     -- 创建球员
'league:player:update',     -- 更新球员
'league:player:delete',     -- 删除球员
'league:player:query',      -- 查询球员
'league:player:export',     -- 导出球员数据

-- 生涯统计权限
'league:player-career-stats:query',      -- 查询生涯数据
'league:player-career-stats:update',     -- 更新生涯数据
'league:player-career-stats:initialize', -- 初始化生涯数据

-- 球员对比权限
'league:player-comparison:query',  -- 查询对比数据
'league:player-comparison:export', -- 导出对比数据

-- 数据排行权限
'league:player-career-leaderboard:query'  -- 查询排行榜
```

## 组件路径配置

### 组件路径规则
- 前缀: `@/views/`
- 路径格式: `模块/子模块/组件名`
- 示例: `league/player/index.vue`

### 组件路径映射
```typescript
// 菜单表中的 component 字段
'league/player/index'              // => @/views/league/player/index.vue
'league/player/career-stats'       // => @/views/league/player/career-stats/index.vue
'league/player/comparison'          // => @/views/league/player/comparison/index.vue
'league/player/career-leaderboard'  // => @/views/league/player/career-leaderboard/index.vue
```

## 路由参数配置

### 动态路由参数
某些页面支持动态参数：
```typescript
// 球员详情页
{
  path: '/league/player/detail/:id',
  component: () => import('@/views/league/player/detail/index.vue')
}
```

### 查询参数
```typescript
// 生涯数据页面支持查询参数
/league/player/career-stats?playerId=123&seasonId=456
```

## 路由守卫配置

### 权限验证
```typescript
// 前端路由守卫
router.beforeEach(async (to, from, next) => {
  // 检查权限
  if (to.meta.permission) {
    const hasPermission = checkPermission(to.meta.permission)
    if (!hasPermission) {
      next('/403')
      return
    }
  }
  next()
})
```

## 国际化配置

### 路由标题配置
```typescript
// 在 @/locales/ 中配置路由标题
{
  'router.league.player': '球员管理',
  'router.league.career-stats': '生涯统计',
  'router.league.comparison': '球员对比',
  'router.league.leaderboard': '数据排行'
}
```

## 部署注意事项

### 1. 数据库配置
- 确保 `system_menu` 表包含所有菜单项
- 确保 `system_role_menu` 表配置角色权限

### 2. 权限缓存
- 系统启动时会缓存菜单配置
- 修改菜单后需要清除缓存或重启服务

### 3. 前端路由
- 动态路由在用户登录后加载
- 开发时可通过直接访问URL测试路由

## 故障排除

### 常见问题
1. **路由无法访问**
   - 检查菜单配置是否正确
   - 确认用户是否有相应权限

2. **页面显示404**
   - 检查组件路径是否正确
   - 确认组件文件是否存在

3. **权限不足**
   - 检查角色权限配置
   - 确认用户角色分配

### 调试方法
```javascript
// 控制台查看当前路由
console.log(router.getRoutes())

// 查看用户权限
console.log(userStore.permissions)
```

## 开发指南

### 新增路由步骤
1. 创建Vue组件文件
2. 在数据库中添加菜单记录
3. 配置权限编码
4. 分配角色权限
5. 测试路由访问

### 组件开发规范
- 使用Vue 3 Composition API
- 遵循项目代码风格
- 实现权限检查
- 支持响应式设计

## 总结

赛点联盟平台使用动态路由系统，通过后端菜单配置统一管理路由和权限。球员生涯统计模块的路由配置已完整实现，包括球员管理、生涯统计、对比分析和数据排行等功能模块。

通过合理的菜单结构和权限配置，实现了灵活的访问控制和用户体验。开发者可以根据业务需求轻松扩展新的功能模块。