# 内管菜单配置 - 联盟排名功能

## 菜单配置概览

基于提供的SQL INSERT语句，以下是联盟排名功能的完整菜单配置：

### 1. 主菜单配置

```sql
-- 联盟排名主菜单
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2910, '联盟排名', '', 2, 11, 2759, 'league-ranking', 'ep:trophy', 
    'operation/leagueRanking/index', 'LeagueRanking', 0, b'1', b'1', b'1', 
    '1', '2025-07-27 21:57:49', '1', '2025-07-27 21:57:49', b'0'
);
```

**配置说明：**
- **菜单ID**: 2910
- **菜单名称**: 联盟排名
- **菜单类型**: 2 (菜单类型)
- **排序**: 11
- **父菜单ID**: 2759 (运营管理模块)
- **路由路径**: league-ranking
- **图标**: ep:trophy (奖杯图标)
- **组件路径**: operation/leagueRanking/index
- **组件名称**: LeagueRanking
- **状态**: 0 (启用)
- **可见性**: 1 (可见)
- **缓存**: 1 (启用缓存)
- **始终显示**: 1 (始终显示)

### 2. 权限配置

```sql
-- 联盟排名查询权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2911, '联盟排名查询', 'operation:league-ranking:query', 3, 1, 2910, '', '#', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2025-07-27 21:57:49', '1', '2025-07-27 21:57:49', b'0'
);
```

**权限说明：**
- **权限ID**: 2911
- **权限名称**: 联盟排名查询
- **权限标识**: operation:league-ranking:query
- **权限类型**: 3 (按钮/权限)
- **排序**: 1
- **父菜单ID**: 2910 (联盟排名菜单)
- **状态**: 0 (启用)
- **可见性**: 1 (可见)

## 菜单层级结构

```
运营管理 (ID: 2759)
└── 联盟排名 (ID: 2910)
    └── 联盟排名查询 (ID: 2911, 权限)
```

## 前端组件配置

### 组件路径
- **主组件**: `operation/leagueRanking/index`
- **组件名称**: `LeagueRanking`

### 路由配置
```javascript
{
  path: '/league-ranking',
  name: 'LeagueRanking',
  component: () => import('@/views/operation/leagueRanking/index.vue'),
  meta: {
    title: '联盟排名',
    icon: 'ep:trophy',
    keepAlive: true,
    alwaysShow: true
  }
}
```

## 权限控制

### 后端权限
- **权限标识**: `operation:league-ranking:query`
- **使用方式**: 在Controller方法上添加 `@PreAuthorize("@ss.hasPermission('operation:league-ranking:query')")`

### 前端权限
```vue
<template>
  <div v-hasPermission="'operation:league-ranking:query'">
    <!-- 联盟排名内容 -->
  </div>
</template>
```

## 功能建议

### 联盟排名功能应包含：
1. **排名展示**
   - 总排名
   - 赛季排名
   - 分组排名

2. **筛选功能**
   - 赛季筛选
   - 分组筛选
   - 时间范围筛选

3. **数据展示**
   - 球队信息
   - 战绩统计
   - 排名变化趋势

4. **导出功能**
   - Excel导出
   - PDF导出

## 相关配置文件

### 1. 权限配置文件
```java
// 权限常量定义
public class LeagueRankingPermission {
    public static final String QUERY = "operation:league-ranking:query";
}
```

### 2. 路由配置文件
```javascript
// router/modules/operation.js
export default {
  path: '/league',
  name: 'League',
  component: Layout,
  redirect: '/league/ranking',
  meta: {
    title: '联盟管理',
    icon: 'ep:trophy'
  },
  children: [
    {
      path: 'ranking',
      name: 'LeagueRanking',
      component: () => import('@/views/operation/leagueRanking/index.vue'),
      meta: {
        title: '联盟排名',
        icon: 'ep:trophy'
      }
    }
  ]
}
```

## 注意事项

1. **菜单顺序**: 排序值为11，请根据实际需求调整
2. **父菜单**: 确保父菜单ID 2759 存在且为运营管理模块
3. **组件路径**: 确保前端组件路径正确
4. **权限分配**: 需要为相关角色分配相应权限
5. **国际化**: 如需支持多语言，请配置对应的国际化文件

## 执行SQL

完整执行SQL：
```sql
-- 1. 插入联盟排名主菜单
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (2910, '联盟排名', '', 2, 11, 2759, 'league-ranking', 'ep:trophy', 'operation/leagueRanking/index', 'LeagueRanking', 0, b'1', b'1', b'1', '1', '2025-07-27 21:57:49', '1', '2025-07-27 21:57:49', b'0');

-- 2. 插入联盟排名查询权限
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (2911, '联盟排名查询', 'operation:league-ranking:query', 3, 1, 2910, '', '#', NULL, NULL, 0, b'1', b'1', b'1', '1', '2025-07-27 21:57:49', '1', '2025-07-27 21:57:49', b'0');
```

## 执行后验证

1. **菜单显示**: 登录后台管理系统，检查联盟排名菜单是否正确显示
2. **权限验证**: 使用不同权限账号验证权限控制是否生效
3. **功能测试**: 测试联盟排名功能是否正常工作
4. **数据准确性**: 验证排名数据的准确性和实时性