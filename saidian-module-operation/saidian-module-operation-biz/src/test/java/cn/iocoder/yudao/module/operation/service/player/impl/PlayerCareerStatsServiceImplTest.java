package cn.iocoder.yudao.module.operation.service.player.impl;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.*;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;
import cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.SeasonMapper;
import cn.iocoder.yudao.module.operation.service.player.bo.PlayerCareerStatsCalculationBO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants.PLAYER_NOT_EXISTS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link PlayerCareerStatsServiceImpl} 的单元测试类
 * 
 * 测试业务逻辑、事务处理和异常场景
 * 
 * <AUTHOR>
 */
@DisplayName("球员生涯统计服务测试")
public class PlayerCareerStatsServiceImplTest extends BaseMockitoUnitTest {

    @InjectMocks
    private PlayerCareerStatsServiceImpl playerCareerStatsService;

    @Mock
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    @Mock
    private PlayerStatisticsMapper playerStatisticsMapper;
    @Mock
    private PlayerMapper playerMapper;
    @Mock
    private GameMapper gameMapper;
    @Mock
    private SeasonMapper seasonMapper;

    @Test
    @DisplayName("测试分页查询球员生涯统计")
    public void testGetPlayerCareerStatsPage() {
        // Given
        PlayerCareerStatsPageReqVO pageReqVO = new PlayerCareerStatsPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);

        List<PlayerCareerStatsDO> mockList = Arrays.asList(
            createMockPlayerCareerStatsDO(1L, 1L, null, null),
            createMockPlayerCareerStatsDO(2L, 2L, null, null)
        );
        PageResult<PlayerCareerStatsDO> mockPageResult = new PageResult<>(mockList, 2L);

        when(playerCareerStatsMapper.selectPage(pageReqVO)).thenReturn(mockPageResult);

        // When
        PageResult<PlayerCareerStatsRespVO> result = playerCareerStatsService.getPlayerCareerStatsPage(pageReqVO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getList()).hasSize(2);
        assertThat(result.getTotal()).isEqualTo(2L);
        verify(playerCareerStatsMapper, times(1)).selectPage(pageReqVO);
    }

    @Test
    @DisplayName("测试获取球员生涯统计详情")
    public void testGetPlayerCareerStatsDetail() {
        // Given
        Long playerId = 1L;
        Long seasonId = null;
        Integer gameType = null;

        PlayerDO mockPlayer = createMockPlayer(playerId, "张三", "avatar.jpg", "23");
        List<SeasonDO> mockSeasons = Arrays.asList(
            createMockSeason(1L, "2023赛季"),
            createMockSeason(2L, "2024赛季")
        );

        when(playerMapper.selectById(playerId)).thenReturn(mockPlayer);
        when(seasonMapper.selectList()).thenReturn(mockSeasons);
        when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(createMockPlayerStatistics());
        when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(createMockGameResults());

        // When
        PlayerCareerStatsDetailRespVO result = playerCareerStatsService.getPlayerCareerStatsDetail(playerId, seasonId, gameType);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPlayerInfo()).isNotNull();
        assertThat(result.getPlayerInfo().getPlayerId()).isEqualTo(playerId);
        assertThat(result.getPlayerInfo().getPlayerName()).isEqualTo("张三");
        assertThat(result.getCareerTotal()).isNotNull();
        assertThat(result.getGameTypeStats()).isNotNull();
        assertThat(result.getSeasonStats()).hasSize(2);

        verify(playerMapper, times(1)).selectById(playerId);
        verify(seasonMapper, times(1)).selectList();
    }

    @Test
    @DisplayName("测试获取不存在球员的统计详情")
    public void testGetPlayerCareerStatsDetailPlayerNotExists() {
        // Given
        Long playerId = 999L;

        when(playerMapper.selectById(playerId)).thenReturn(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () ->
            playerCareerStatsService.getPlayerCareerStatsDetail(playerId, null, null)
        );

        assertThat(exception.getCode()).isEqualTo(PLAYER_NOT_EXISTS.getCode());
        verify(playerMapper, times(1)).selectById(playerId);
    }

    @Test
    @DisplayName("测试计算并更新球员生涯统计")
    public void testCalculateAndUpdatePlayerCareerStats() {
        // Given
        Long playerId = 1L;

        PlayerDO mockPlayer = createMockPlayer(playerId, "张三", "avatar.jpg", "23");
        List<SeasonDO> mockSeasons = Arrays.asList(
            createMockSeason(1L, "2023赛季"),
            createMockSeason(2L, "2024赛季")
        );

        when(playerMapper.selectById(playerId)).thenReturn(mockPlayer);
        when(seasonMapper.selectList()).thenReturn(mockSeasons);
        when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(createMockPlayerStatistics());
        when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(createMockGameResults());

        // When
        playerCareerStatsService.calculateAndUpdatePlayerCareerStats(playerId);

        // Then
        verify(playerMapper, times(1)).selectById(playerId);
        verify(playerCareerStatsMapper, times(1)).deleteByPlayerIdAndCondition(playerId, null, null);
        verify(playerCareerStatsMapper, times(1)).insertBatch(anyList());
    }

    @Test
    @DisplayName("测试计算不存在球员的生涯统计")
    public void testCalculateAndUpdatePlayerCareerStatsPlayerNotExists() {
        // Given
        Long playerId = 999L;

        when(playerMapper.selectById(playerId)).thenReturn(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () ->
            playerCareerStatsService.calculateAndUpdatePlayerCareerStats(playerId)
        );

        assertThat(exception.getCode()).isEqualTo(PLAYER_NOT_EXISTS.getCode());
        verify(playerMapper, times(1)).selectById(playerId);
        verify(playerCareerStatsMapper, never()).deleteByPlayerIdAndCondition(anyLong(), any(), any());
    }

    @Test
    @DisplayName("测试批量计算所有球员生涯统计")
    public void testCalculateAndUpdateAllPlayerCareerStats() {
        // Given
        PlayerCareerStatsRefreshReqVO refreshReqVO = new PlayerCareerStatsRefreshReqVO();

        List<PlayerDO> mockPlayers = Arrays.asList(
            createMockPlayer(1L, "张三", "avatar1.jpg", "23"),
            createMockPlayer(2L, "李四", "avatar2.jpg", "24"),
            createMockPlayer(3L, "王五", "avatar3.jpg", "25")
        );

        lenient().when(playerMapper.selectList()).thenReturn(mockPlayers);
        lenient().when(seasonMapper.selectList()).thenReturn(Arrays.asList(createMockSeason(1L, "2023赛季")));
        lenient().when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(createMockPlayerStatistics());
        lenient().when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(createMockGameResults());

        // When
        playerCareerStatsService.calculateAndUpdateAllPlayerCareerStats(refreshReqVO);

        // 由于是异步方法，等待一下
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        verify(playerMapper, times(1)).selectList();
        // 注意：实际实现可能不调用这些方法，取决于具体的业务逻辑
        // 只验证核心方法被调用
        verify(playerMapper, atLeast(1)).selectList();
    }

    @Test
    @DisplayName("测试没有球员时的批量计算")
    public void testCalculateAndUpdateAllPlayerCareerStatsNoPlayers() {
        // Given
        PlayerCareerStatsRefreshReqVO refreshReqVO = new PlayerCareerStatsRefreshReqVO();

        when(playerMapper.selectList()).thenReturn(Collections.emptyList());

        // When
        playerCareerStatsService.calculateAndUpdateAllPlayerCareerStats(refreshReqVO);

        // Then
        verify(playerMapper, times(1)).selectList();
        verify(playerCareerStatsMapper, never()).deleteByPlayerIdAndCondition(anyLong(), any(), any());
        verify(playerCareerStatsMapper, never()).insertBatch(anyList());
    }

    @Test
    @DisplayName("测试球员统计数据变更触发更新")
    public void testOnPlayerStatisticsChanged() {
        // Given
        Long playerId = 1L;
        Long gameId = 1L;

        PlayerDO mockPlayer = createMockPlayer(playerId, "张三", "avatar.jpg", "23");
        when(playerMapper.selectById(playerId)).thenReturn(mockPlayer);
        when(seasonMapper.selectList()).thenReturn(Arrays.asList(createMockSeason(1L, "2023赛季")));
        when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(createMockPlayerStatistics());
        when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(createMockGameResults());

        // When
        playerCareerStatsService.onPlayerStatisticsChanged(playerId, gameId);

        // Then
        verify(playerMapper, times(1)).selectById(playerId);
        verify(playerCareerStatsMapper, times(1)).deleteByPlayerIdAndCondition(playerId, null, null);
        verify(playerCareerStatsMapper, times(1)).insertBatch(anyList());
    }

    @Test
    @DisplayName("测试获取球员生涯统计数据")
    public void testGetPlayerCareerStats() {
        // Given
        Long playerId = 1L;
        Long seasonId = 1L;
        Integer gameType = 1;

        List<PlayerCareerStatsDO> mockStats = Arrays.asList(
            createMockPlayerCareerStatsDO(1L, playerId, seasonId, gameType)
        );

        when(playerCareerStatsMapper.selectByPlayerIdAndCondition(playerId, seasonId, gameType))
            .thenReturn(mockStats);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsService.getPlayerCareerStats(playerId, seasonId, gameType);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getPlayerId()).isEqualTo(playerId);
        verify(playerCareerStatsMapper, times(1)).selectByPlayerIdAndCondition(playerId, seasonId, gameType);
    }

    @Test
    @DisplayName("测试计算球员生涯统计")
    public void testCalculatePlayerCareerStats() {
        // Given
        Long playerId = 1L;
        Long seasonId = null;
        Integer gameType = null;

        when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(createMockPlayerStatistics());
        when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(createMockGameResults());

        // When
        PlayerCareerStatsCalculationBO result = playerCareerStatsService.calculatePlayerCareerStats(playerId, seasonId, gameType);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalGames()).isGreaterThan(0);
        verify(playerStatisticsMapper, times(1)).selectPlayerStatisticsByCondition(anyMap());
        verify(gameMapper, times(1)).selectGameResultsByPlayer(anyMap());
    }

    @Test
    @DisplayName("测试导出球员生涯统计列表")
    public void testGetPlayerCareerStatsListForExport() {
        // Given
        PlayerCareerStatsPageReqVO pageReqVO = new PlayerCareerStatsPageReqVO();

        List<PlayerCareerStatsDO> mockList = Arrays.asList(
            createMockPlayerCareerStatsDO(1L, 1L, null, null),
            createMockPlayerCareerStatsDO(2L, 2L, null, null)
        );

        when(playerCareerStatsMapper.selectPlayerCareerStatsWithPlayerInfo(pageReqVO))
            .thenReturn(mockList);

        // When
        List<PlayerCareerStatsRespVO> result = playerCareerStatsService.getPlayerCareerStatsListForExport(pageReqVO);

        // Then
        assertThat(result).hasSize(2);
        verify(playerCareerStatsMapper, times(1)).selectPlayerCareerStatsWithPlayerInfo(pageReqVO);
    }

    @Test
    @DisplayName("测试异常处理：数据库操作失败")
    public void testCalculateAndUpdatePlayerCareerStatsWithDatabaseError() {
        // Given
        Long playerId = 1L;

        PlayerDO mockPlayer = createMockPlayer(playerId, "张三", "avatar.jpg", "23");
        when(playerMapper.selectById(playerId)).thenReturn(mockPlayer);
        doThrow(new RuntimeException("Database error")).when(playerCareerStatsMapper)
            .deleteByPlayerIdAndCondition(playerId, null, null);

        // When & Then
        assertThrows(RuntimeException.class, () ->
            playerCareerStatsService.calculateAndUpdatePlayerCareerStats(playerId)
        );

        verify(playerMapper, times(1)).selectById(playerId);
        verify(playerCareerStatsMapper, times(1)).deleteByPlayerIdAndCondition(playerId, null, null);
        verify(playerCareerStatsMapper, never()).insertBatch(anyList());
    }

    @Test
    @DisplayName("测试性能：大量数据计算")
    public void testCalculatePlayerCareerStatsWithLargeDataset() {
        // Given
        Long playerId = 1L;
        
        // 创建大量统计数据（模拟1000场比赛）
        List<PlayerStatisticsDO> largeStatistics = new ArrayList<>();
        List<PlayerCareerStatsCalculationBO.GameResultBO> largeGameResults = new ArrayList<>();
        
        for (int i = 1; i <= 1000; i++) {
            largeStatistics.add(createMockPlayerStatistic((long) i, playerId));
            largeGameResults.add(new PlayerCareerStatsCalculationBO.GameResultBO(
                (long) i, LocalDateTime.now().minusDays(i), i % 2 == 0));
        }

        when(playerStatisticsMapper.selectPlayerStatisticsByCondition(anyMap()))
            .thenReturn(largeStatistics);
        when(gameMapper.selectGameResultsByPlayer(anyMap()))
            .thenReturn(largeGameResults);

        // When
        long startTime = System.currentTimeMillis();
        PlayerCareerStatsCalculationBO result = playerCareerStatsService.calculatePlayerCareerStats(playerId, null, null);
        long endTime = System.currentTimeMillis();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalGames()).isEqualTo(1000);
        assertThat(endTime - startTime).isLessThan(1000); // 应在1秒内完成
    }

    // ==================== 辅助方法 ====================

    private PlayerCareerStatsDO createMockPlayerCareerStatsDO(Long id, Long playerId, Long seasonId, Integer gameType) {
        return PlayerCareerStatsDO.builder()
            .id(id)
            .playerId(playerId)
            .seasonId(seasonId)
            .gameType(gameType)
            .totalGames(50)
            .wins(30)
            .losses(20)
            .winRate(BigDecimal.valueOf(60.00))
            .totalPoints(1000)
            .totalAssists(250)
            .totalRebounds(300)
            .avgPoints(BigDecimal.valueOf(20.00))
            .avgAssists(BigDecimal.valueOf(5.00))
            .avgRebounds(BigDecimal.valueOf(6.00))
            .fieldGoalPercentage(BigDecimal.valueOf(45.5))
            .playerEfficiencyRating(BigDecimal.valueOf(18.5))
            .doubleDoubles(15)
            .tripleDoubles(2)
            .build();
    }

    private PlayerDO createMockPlayer(Long id, String name, String avatar, String number) {
        PlayerDO player = new PlayerDO();
        player.setId(id);
        player.setName(name);
        player.setAvatar(avatar);
        player.setNumber(Integer.valueOf(number));
        return player;
    }

    private SeasonDO createMockSeason(Long id, String seasonName) {
        SeasonDO season = new SeasonDO();
        season.setId(id);
        season.setSeasonName(seasonName);
        return season;
    }

    private List<PlayerStatisticsDO> createMockPlayerStatistics() {
        return Arrays.asList(
            createMockPlayerStatistic(1L, 1L),
            createMockPlayerStatistic(2L, 1L),
            createMockPlayerStatistic(3L, 1L)
        );
    }

    private PlayerStatisticsDO createMockPlayerStatistic(Long gameId, Long playerId) {
        return PlayerStatisticsDO.builder()
            .gameId(gameId)
            .playerId(playerId)
            .playingTime(1800) // 30分钟
            .points(20)
            .assists(5)
            .steals(2)
            .blocks(1)
            .turnovers(3)
            .fouls(2)
            .twoPointAttempts(8)
            .twoPointMakes(4)
            .threePointAttempts(6)
            .threePointMakes(2)
            .freeThrowAttempts(4)
            .freeThrowMakes(3)
            .offensiveRebounds(2)
            .defensiveRebounds(4)
            .efficiency(18)
            .plusMinus(5)
            .build();
    }

    private List<PlayerCareerStatsCalculationBO.GameResultBO> createMockGameResults() {
        return Arrays.asList(
            new PlayerCareerStatsCalculationBO.GameResultBO(1L, LocalDateTime.now().minusDays(2), true),
            new PlayerCareerStatsCalculationBO.GameResultBO(2L, LocalDateTime.now().minusDays(1), true),
            new PlayerCareerStatsCalculationBO.GameResultBO(3L, LocalDateTime.now(), false)
        );
    }
}