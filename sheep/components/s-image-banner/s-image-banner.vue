<!-- 装修图文组件：图片轮播 -->
<template>
  <view class="s-banner-container">
    <su-swiper
      :list="imgList"
      :dotStyle="data.indicator === 'dot' ? 'long' : 'tag'"
      imageMode="aspectFill"
      dotCur="bg-mask-40"
      :seizeHeight="280"
      :height="280"
      :autoplay="data.autoplay"
      :interval="data.interval * 1000"
      :mode="data.type"
      :imgTopRadius="12"
      :imgBottomRadius="12"
      bg="bg-none"
    />
  </view>
</template>

<script setup>
  import { computed } from 'vue';
  import sheep from '@/sheep';

  // 轮播图
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
    styles: {
      type: Object,
      default: () => ({}),
    },
  });

  const imgList = computed(() =>
      props.data.items.map((item) => {
        const src = item.type === 'img' ? item.imgUrl : item.videoUrl;
        return {
          ...item,
          type: item.type === 'img' ? 'image' : 'video',
          src: sheep.$url.cdn(src),
          poster: sheep.$url.cdn(item.imgUrl),
        };
      }),
  );
</script>

<style lang="scss" scoped>
.s-banner-container {
  position: relative;
  margin: 16rpx 8rpx;
  
  :deep(.ui-swiper) {
    margin: 0;
  }
  
  .banner-title {
    position: absolute;
    left: 0;
    top: 20rpx;
    background-color: rgba(216, 85, 85, 0.8);
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    padding: 12rpx 30rpx 12rpx 20rpx;
    border-top-right-radius: 50rpx;
    border-bottom-right-radius: 50rpx;
    z-index: 10;
    transform: translateX(-3rpx);
    
    text {
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }
}
</style>
