<template>
  <view class="index-register-wrap" :style="[bgStyle]">
    <!-- 报名卡片 -->
    <view class="card register-card" @tap="toRegister">
      <view class="register-content">
        <view class="register-left">
          <text class="register-title">报名</text>
        </view>
        <view class="register-right">
          <template v-if="hasNextGame">
            <text class="register-status">下一场比赛倒计时</text>
            <text class="register-countdown">
              <text class="day">{{ countdown.days }}</text>
              <text class="unit">天</text>
              <text class="time">{{ countdown.hours }}:{{ countdown.minutes }}:{{ countdown.seconds }}</text>
            </text>
          </template>
          <text class="register-countdown no-game" v-else>近期无比赛</text>
        </view>
      </view>
    </view>
    
    <!-- 赛程和排行榜卡片 -->
    <view class="menu-container">
      <view class="card menu-card" @tap="toSchedule">
        <text class="menu-text">赛程</text>
      </view>
      <view class="card menu-card" @tap="toRanking">
        <text class="menu-text">排行榜</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import sheep from '@/sheep';
import GameApi from '@/sheep/api/operation/game';
import dayjs from 'dayjs';

// 接收参数
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
});

// 设置背景样式
const bgStyle = computed(() => {
  const { bgType, bgImg, bgColor, marginLeft, marginRight, marginBottom } = props.styles;
  
  return {
    background: bgType === 'img' ? `url(${bgImg}) no-repeat top center / 100% 100%` : bgColor,
    marginLeft: marginLeft + 'rpx',
    marginRight: marginRight + 'rpx',
    marginBottom: marginBottom + 'rpx'
  };
});

// 跳转方法
function toRegister() {
  sheep.$router.go('/pages/index/game');
}

function toSchedule() {
  sheep.$router.go('/pages/game/all-schedule');
}

function toRanking() {
  sheep.$router.go('/pages/player/player-ranking-list');
}

// 倒计时相关的状态
const nextGame = ref(null);
const countdown = ref({
  days: '00',
  hours: '00',
  minutes: '00',
  seconds: '00'
});
let timer = null;

// 获取最近一场比赛
async function getNextGame() {
  try {
    const { code, data } = await GameApi.getNextGame();
    if (code === 0 && data) {
      nextGame.value = data;
      startCountdown();
    }
  } catch (error) {
    console.error('获取最近比赛失败:', error);
  }
}

// 计算倒计时
function calculateCountdown() {
  if (!nextGame.value || !nextGame.value.startTime) {
    return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00'
    };
  }

  const now = dayjs();
  const gameTime = dayjs(nextGame.value.startTime);
  const diff = gameTime.diff(now, 'second');

  if (diff <= 0) {
    clearInterval(timer);
    return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00'
    };
  }

  const days = Math.floor(diff / (24 * 60 * 60));
  const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((diff % (60 * 60)) / 60);
  const seconds = diff % 60;

  return {
    days: String(days).padStart(2, '0'),
    hours: String(hours).padStart(2, '0'),
    minutes: String(minutes).padStart(2, '0'),
    seconds: String(seconds).padStart(2, '0')
  };
}

// 开始倒计时
function startCountdown() {
  if (timer) {
    clearInterval(timer);
  }
  
  // 立即更新一次
  countdown.value = calculateCountdown();
  
  // 设置定时器，每秒更新一次
  timer = setInterval(() => {
    countdown.value = calculateCountdown();
  }, 1000);
}

// 判断是否有下一场比赛
const hasNextGame = computed(() => {
  return nextGame.value !== null && nextGame.value.startTime !== null;
});

// 组件挂载时获取比赛信息并开始倒计时
onMounted(() => {
  getNextGame();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style lang="scss" scoped>
.index-register-wrap {
  padding: 20rpx;
  
  // 通用卡片样式
  .card {
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
  // 报名卡片
  .register-card {
    padding: 40rpx;
    margin-bottom: 20rpx;
    
    .register-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .register-left {
        .register-title {
          font-size: 96rpx;
          font-weight: bold;
          color: #0014B7;
          font-style: italic;
        }
      }
      
      .register-right {
        flex: 1;
        text-align: center;
        padding-top: 10rpx;
        min-height: 120rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 40rpx;
        
        .register-status {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
        }
        
        .register-countdown {
          .day {
            font-size: 36rpx;
            color: #E4393C;
            font-weight: bold;
          }
          
          .unit {
            font-size: 32rpx;
            color: #333;
            margin: 0 10rpx;
          }
          
          .time {
            font-size: 36rpx;
            color: #E4393C;
            font-weight: bold;
          }
          
          &.no-game {
            font-size: 28rpx;
            color: #999;
          }
        }
      }
    }
  }
  
  // 菜单区域
  .menu-container {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
    
    .menu-card {
      flex: 1;
      height: 200rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .menu-text {
        font-size: 64rpx;
        font-weight: bold;
        color: #0014B7;
        font-style: italic;
      }
    }
  }
}
</style> 