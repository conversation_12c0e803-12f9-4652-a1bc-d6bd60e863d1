<template>
  <view class="register-card">
    <!-- 头部区域 -->
    <view class="activity-header">
      <view class="activity-title">{{ activity.title || '活动标题' }}</view>
      <view class="activity-tag">
        <text v-if="activity.activityType === 1">排位赛</text>
        <text v-else-if="activity.activityType === 2">友谊赛</text>
        <text v-else>联赛</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="activity-content">
      <view class="activity-thumbnail">
        <image :src="activity.picUrl || '/static/images/default-activity.png'" mode="aspectFill" />
      </view>

      <view class="activity-info">
        <!-- 时间 -->
        <view class="activity-detail">
          <uni-icons type="calendar" size="16" color="#999"></uni-icons>
          <text class="value">{{ activity.startTime ? formatDate(activity.startTime) : '2025-03-18 20:00' }}</text>
        </view>
        <!-- 地点 -->
        <view class="activity-detail">
          <uni-icons type="location" size="16" color="#999"></uni-icons>
          <text class="value">{{ activity.location || '福田区PW篮球公园（香梅路）' }}</text>
        </view>
        <!-- 比赛类型 -->
        <view class="activity-detail">
          <uni-icons type="staff" size="16" color="#999"></uni-icons>
          <text class="value">{{ activity.gameType ? getGameTypeText(activity.gameType) : (activity.gameConfig ||
            '全场5人制篮球赛')
          }}</text>
        </view>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="activity-actions">
      <view class="activity-stats">
        <text class="price">¥{{ activity.price ? formatPrice(activity.price) : '58' }}</text>
        <text class="unit">{{ activity.priceUnit || '/人' }}</text>
      </view>
      <view class="activity-status">
        <text class="register-status" :class="{ 'full': isRegistrationFull }">{{ props.activity.currentRegisters || 0
        }}</text><text>/{{ props.activity.maxRegisters || 24 }}</text>
        <text class="deadline">{{ activity.startTime ? getTimeLeft(activity.startTime) : '5天后开赛'
        }}</text>
      </view>
      
      <!-- 根据活动状态显示不同的按钮或状态 -->
      <button 
        v-if="shouldShowRegisterButton" 
        class="activity-join" 
        :class="{ 'disabled': isRegistrationFull }"
        @tap="handleRegister"
      >
        {{ getRegisterButtonText }}
      </button>
      
      <!-- 非报名状态显示状态文字 -->
      <view v-else class="activity-status-text" :class="getStatusClass">
        {{ getStatusText }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// 活动状态枚举 - 与后端保持一致
const ACTIVITY_STATUS = {
  DRAFT: 0,           // 草稿
  NOT_STARTED: 1,     // 未开始
  REGISTRATION: 2,    // 报名中
  IN_PROGRESS: 4,     // 进行中
  COMPLETED: 5,       // 已结束
  CANCELLED: 6,       // 已取消
  GROUPING_FAILED: 7, // 组局失败
  GROUPING_SUCCESSFUL: 8, // 组局成功
  REFUNDING: 9        // 退款中
};

// 定义props
const props = defineProps({
  activity: {
    type: Object,
    default: () => ({})
  }
});

// 根据游戏类型获取配置文本
const getGameTypeText = (gameType) => {
  switch (gameType) {
    case 1:
      return "全场5人制篮球赛";
    case 2:
      return "半场4人制篮球赛";
    default:
      return "篮球赛";
  }
};

// 计算注册是否已满
const isRegistrationFull = computed(() => {
  const activity = props.activity;
  if (!activity) return false;

  // 根据活动类型判断满员条件
  if (activity.activityType === 1) {
    // 排位赛：按人数判断
    return (activity.currentRegisters || 0) >= (activity.maxRegisters || 20);
  } else if (activity.activityType === 2 || activity.activityType === 3) {
    // 友谊赛或联赛：按报名成功的球队数量判断
    // 后端已修改 signedUpTeams 只包含满足最小人数的球队
    const currentSuccessfulTeams = activity.signedUpTeams ? activity.signedUpTeams.length : 0;
    const maxTeams = activity.maxTeams || (activity.activityType === 2 ? 2 : 16); // 友谊赛默认2队，联赛默认16队
    return currentSuccessfulTeams >= maxTeams;
  }
  
  // 默认按人数判断
  return (activity.currentRegisters || 0) >= (activity.maxRegisters || 20);
});

// 判断是否应该显示报名按钮
const shouldShowRegisterButton = computed(() => {
  const status = props.activity.status;
  // 只有在报名中和组局成功状态下才显示报名按钮
  return status === ACTIVITY_STATUS.REGISTRATION || 
         status === ACTIVITY_STATUS.GROUPING_SUCCESSFUL;
});

// 获取报名按钮文字
const getRegisterButtonText = computed(() => {
  if (isRegistrationFull.value) {
    return '候补报名';
  }
  return '立即报名';
});

// 获取状态文字
const getStatusText = computed(() => {
  const status = props.activity.status;
  switch (status) {
    case ACTIVITY_STATUS.DRAFT:
      return '草稿';
    case ACTIVITY_STATUS.NOT_STARTED:
      return '未开始';
    case ACTIVITY_STATUS.IN_PROGRESS:
      return '进行中';
    case ACTIVITY_STATUS.COMPLETED:
      return '已结束';
    case ACTIVITY_STATUS.CANCELLED:
      return '已取消';
    case ACTIVITY_STATUS.GROUPING_FAILED:
      return '组局失败';
    case ACTIVITY_STATUS.REFUNDING:
      return '退款中';
    default:
      return '查看详情';
  }
});

// 获取状态样式类
const getStatusClass = computed(() => {
  const status = props.activity.status;
  switch (status) {
    case ACTIVITY_STATUS.IN_PROGRESS:
      return 'status-in-progress';
    case ACTIVITY_STATUS.COMPLETED:
      return 'status-completed';
    case ACTIVITY_STATUS.CANCELLED:
    case ACTIVITY_STATUS.GROUPING_FAILED:
      return 'status-cancelled';
    case ACTIVITY_STATUS.REFUNDING:
      return 'status-refunding';
    default:
      return 'status-default';
  }
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0';
  return (price / 100).toFixed(2);
};

// 计算倒计时（智能展示天、小时、分钟、秒）
const getTimeLeft = (startTime) => {
  if (!startTime) return '5天后开赛';

  const now = new Date().getTime();
  const start = new Date(startTime).getTime();
  const diff = start - now;

  // 已过期（比赛已开始）
  if (diff <= 0) {
    return '已开赛';
  }

  // 计算剩余时间
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  // 根据剩余时间展示不同单位
  if (days > 0) {
    return `${days}天后开赛`;
  } else if (hours > 0) {
    return `${hours}小时后开赛`;
  } else if (minutes > 0) {
    return `${minutes}分钟后开赛`;
  } else {
    return `${seconds}秒后开赛`;
  }
};

// 点击报名按钮处理
const handleRegister = () => {
  if (!props.activity.id) return;
  
  // 如果报名已满，提示用户
  if (isRegistrationFull.value) {
    uni.showToast({
      title: '报名已满，将进入候补队列',
      icon: 'none',
      duration: 2000
    });
  }

  // 跳转到活动详情页
  uni.navigateTo({
    url: `/pages/activity/detail?id=${props.activity.id}`
  });
};
</script>

<style lang="scss" scoped>
.register-card {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);

  .activity-header {
    background: linear-gradient(135deg, #4a90e2, #5c6bc0);
    padding: 24rpx 30rpx;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .activity-title {
      font-size: 32rpx;
      font-weight: 600;
    }

    .activity-tag {
      background-color: rgba(255, 255, 255, 0.3);
      padding: 8rpx 20rpx;
      border-radius: 40rpx;
      font-size: 24rpx;
    }
  }

  .activity-content {
    padding: 20rpx 30rpx;
    display: flex;
    gap: 20rpx;

    .activity-thumbnail {
      width: 140rpx;
      height: 140rpx;
      border-radius: 16rpx;
      overflow: hidden;
      flex-shrink: 0;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .activity-info {
      flex: 1;

      .activity-detail {
        margin-bottom: 10rpx;
        font-size: 28rpx;
        color: #666;
        display: flex;
        align-items: center;

        /* 调整uni-icons组件与文本的间距 */
        :deep(.uni-icons) {
          margin-right: 14rpx;
          width: 40rpx;
          text-align: center;
          line-height: 40rpx;
        }

        /* 篮球图标样式 */
        .basketball-icon {
          margin-right: 14rpx;
          width: 40rpx;
          text-align: center;
          line-height: 40rpx;

          .iconfont {
            font-size: 16px;
            color: #999;
          }
        }

        .value {
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }

  .activity-actions {
    display: flex;
    justify-content: space-between;
    padding: 16rpx 30rpx;
    border-top: 1px solid #f0f0f0;
    align-items: center;

    .activity-stats {
      .price {
        font-size: 32rpx;
        font-weight: bold;
        color: #FF4D4F;
      }

      .unit {
        font-size: 24rpx;
        color: #999;
      }
    }

    .activity-status {
      flex: 1;
      font-size: 24rpx;
      color: #666;
      margin-left: 20rpx;

      .register-status {
        margin-right: 4rpx;

        &.full {
          color: #E57373; // 较柔和的红色
        }
      }

      .status-text {
        margin-right: 4rpx;
      }

      .deadline {
        color: #999;
        margin-left: 10rpx;
      }
    }

    .activity-join {
      min-width: 160rpx;
      height: 60rpx;
      line-height: 60rpx;
      background: #4080FF;
      color: #fff;
      font-size: 28rpx;
      padding: 0 30rpx;
      border-radius: 30rpx;
      margin: 0;
      box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.3);
      
      &.disabled {
        background: #FFA726;
        box-shadow: 0 4rpx 16rpx rgba(255, 167, 38, 0.3);
      }
    }
    
    // 状态文字样式
    .activity-status-text {
      min-width: 160rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      font-size: 28rpx;
      border-radius: 30rpx;
      padding: 0 30rpx;
      
      &.status-in-progress {
        background: #E8F5E8;
        color: #4CAF50;
        border: 1px solid #4CAF50;
      }
      
      &.status-completed {
        background: #F3E5F5;
        color: #9C27B0;
        border: 1px solid #9C27B0;
      }
      
      &.status-cancelled {
        background: #FFEBEE;
        color: #F44336;
        border: 1px solid #F44336;
      }
      
      &.status-refunding {
        background: #FFF3E0;
        color: #FF9800;
        border: 1px solid #FF9800;
      }
      
      &.status-default {
        background: #F5F5F5;
        color: #666;
        border: 1px solid #E0E0E0;
      }
    }
  }
}
</style>