<!-- 装修营销组件：营销文章 -->
<template>
  <view
    :style="[
      {
        marginLeft: styles.marginLeft + 'px',
        marginRight: styles.marginRight + 'px',
        marginBottom: styles.marginBottom + 'px',
        marginTop: styles.marginTop + 'px',
        padding: styles.padding + 'px',
      },
    ]"
  >
    <mp-html class="richtext" :content="state.content"></mp-html>
  </view>
</template>
<script setup>
  import { reactive, onMounted } from 'vue';
  import ArticleApi from '@/sheep/api/promotion/article';

  const props = defineProps({
    data: {
      type: Object,
      default: {},
    },
    styles: {
      type: Object,
      default() {},
    },
  });

  const state = reactive({
    content: '',
  });

  onMounted(async () => {
    console.log(props.data);
    const { data } = await ArticleApi.getArticle(props.data.id,props.data.title);
    state.content = data.content;
  });
</script>
