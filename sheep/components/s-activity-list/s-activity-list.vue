<template>
  <view class="activity-list-container">
    <!-- 标题和查看全部区域 -->
    <view class="section-header">
      <text class="section-title">{{ componentTitle }}</text>
      <view class="view-all-wrap">
        <view class="view-all-btn" @tap="goToActivityList">
          <text>查看全部</text>
          <uni-icons type="arrow-right" size="12" color="#666"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 活动卡片列表 -->
    <view class="card-list">
      <template v-if="filteredActivities.length > 0">
        <s-register-card 
          v-for="(item, index) in filteredActivities" 
          :key="index" 
          :activity="item"
        />
      </template>
      <view v-else-if="loading" class="empty-tip">加载中...</view>
      <view v-else class="empty-tip">暂无活动信息</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ActivityApi from '@/sheep/api/operation/activity';

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: '热门活动'
  },
  activities: {
    type: Array,
    default: () => []
  },
  // 新增：支持模板系统传递的data参数
  data: {
    type: Object,
    default: () => ({})
  },
  // 新增：支持模板系统传递的styles参数
  styles: {
    type: Object,
    default: () => ({})
  }
});

// 加载状态
const loading = ref(false);

// 活动列表数据
const activityList = ref([]);

// 计算标题，优先使用data中的title
const componentTitle = computed(() => {
  return props.data?.title || props.title || '热门活动';
});

// 跳转到活动列表页面
const goToActivityList = () => {
  uni.navigateTo({
    url: '/pages/activity/list'
  });
};

// 筛选后的活动列表 - 后端已处理筛选，前端直接返回数据
const filteredActivities = computed(() => {
  // 数据优先级：props.activities > props.data.activities > activityList
  if (props.activities && props.activities.length > 0) {
    return props.activities;
  } else if (props.data && props.data.activities && props.data.activities.length > 0) {
    return props.data.activities;
  } else {
    return activityList.value;
  }
});

// 获取活动列表数据 - 使用首页专用接口
const loadActivities = async () => {
  loading.value = true;
  try {
    const res = await ActivityApi.getHomeRecommendedActivities(10);
    
    if (res.code === 0 && res.data) {
      activityList.value = res.data;
    } else {
      activityList.value = [];
    }
  } catch (error) {
    console.error('获取推荐活动列表失败', error);
    activityList.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  // 检查是否有任何活动数据，如果都没有才从API获取
  const hasActivities = (props.activities && props.activities.length > 0) || 
                       (props.data && props.data.activities && props.data.activities.length > 0);
  
  if (!hasActivities) {
    loadActivities();
  }
});
</script>

<style lang="scss" scoped>
.activity-list-container {
  padding: 0 20rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 20rpx 0;
    
    .section-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
    
    .view-all-wrap {
      position: relative;
    }
    
    .view-all-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 20rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #666;
      background-color: #f5f5f5;
      transition: all 0.2s;
      
      &:active {
        background-color: #e0e0e0;
      }
      
      text {
        margin-right: 8rpx;
      }
    }
  }
  
  .card-list {
    .empty-tip {
      text-align: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;
    }
  }
}
</style> 