import request from '@/sheep/request';

export default {
  // 提交报名
  submit(data) {
    return request({
      url: '/signup',
      method: 'POST',
      data
    });
  },

  // 取消报名
  cancel(signupId, data) {
    return request({
      url: `/signup/${signupId}/cancel`,
      method: 'POST',
      data
    });
  },

  // 获取我的报名记录列表
  getMyList(params) {
    return request({
      url: '/signup/my',
      method: 'GET',
      params
    });
  }
}; 