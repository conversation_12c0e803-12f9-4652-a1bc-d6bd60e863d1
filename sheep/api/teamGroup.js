import request from '@/sheep/request';

export default {
  // 加入临时组队房间
  join(data) {
    return request({
      url: '/activity/team-group/join',
      method: 'POST',
      data
    });
  },

  // 获取临时组队房间详情
  getDetail(roomId) {
    return request({
      url: `/activity/team-group/${roomId}`,
      method: 'GET'
    });
  },

  // 解散临时组队房间
  dismiss(roomId) {
    return request({
      url: `/activity/team-group/${roomId}`,
      method: 'DELETE'
    });
  }
}; 