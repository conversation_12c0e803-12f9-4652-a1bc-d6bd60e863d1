import request from '@/sheep/request';

const GameApi = {
  // 分页获取赛程列表
  getSchedules: (params) => {
    return request({
      url: '/game/schedule/page',
      method: 'GET',
      params,
    });
  },
  getTeamSchedules: (params) => {
    return request({
      url: '/game/team/schedule/page',
      method: 'GET',
      params,
    });
  },
  getGameInfo: (params) => {
    return request({
      url: '/game/result/get',
      method: 'GET',
      custom: {
        showLoading: true,
        auth: true,
      },
      params
    });
  },
  /**
   * 获取赛程列表
   * @param {Object} params
   * @param {string} params.cursorTime - 游标时间，格式：YYYY-MM-DD
   * @param {string} params.direction - 加载方向：previous(历史)/next(未来)/current(当前)
   * @param {number} params.pageSize - 每页数量
   * @returns {Promise}
   */

  getScheduleList: (params) => {
    return request({
      url: '/game/schedule/scroll',
      method: 'GET',
      params,
    });
  },

  // 获取最近一场比赛
  getNextGame: () => {
    return request({
      url: '/game/schedule/next',
      method: 'GET'
    });
  }
};


export default GameApi;
