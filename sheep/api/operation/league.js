import request from '@/sheep/request';

const LeagueApi = {

  // 分页获取日常活动列表
  getLeagueList: (params) => {
    return request({
      url: '/league/page',
      method: 'GET',
      params,
    });
  },

  getLeagueInfoById: (params) => {
    return request({
      url: '/league/info',
      method: 'GET',
      params,
    });
  },

  // 获取联赛信息
  getLeagueInfo: (params) => {
    // 模拟异步请求
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 0,
          data: {
            id: params.leagueId,
            title: "周末篮球联赛",
            picUrl: "/static/images/league-banner.png",
            startTime: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后
            registrationEndTime: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5天后截止报名
            description: `<div class="league-desc">
              <h2>赛事介绍</h2>
              <p>欢迎参加周末篮球联赛！本次比赛将为广大篮球爱好者提供一个展示球技、以球会友的平台。</p>
              
              <h3>比赛规则</h3>
              <ul>
                <li>采用FIBA国际篮联规则</li>
                <li>每场比赛4节，每节10分钟</li>
                <li>24秒进攻时间限制</li>
              </ul>
              
              <h3>奖项设置</h3>
              <p>冠军队伍：奖金10000元<br>
                 亚军队伍：奖金5000元<br>
                 季军队伍：奖金3000元</p>
            </div>`,
            location: "某某篮球馆",
            type: 1, // 常规赛
            minPlayers: 5,
            maxPlayers: 12,
            gameConfig: "5V5 全场比赛",
            status: 1, // 有效状态
            isRegistration: false, // 是否已报名
            orderId: "", // 订单ID，已报名时才有值

            // 新增球队报名信息
            teamRegisterInfos:[
              {id:1,
                teamLogo: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                teamName: '华强南湖人队',
                currentCount: 8,  // 当前报名人数
                maxCount: 30,     // 最大报名人数
                players: [
                  // {
                  //   id: 1,
                  //   name: '詹姆斯',
                  //   avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                  //   number: '23',
                  //   position: 1
                  // },
                  // {
                  //   id: 2,
                  //   name: '韦德',
                  //   avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                  //   number: '3',
                  //   position: 2
                  // },
                  // {
                  //   id: 3,
                  //   name: '奥尼尔',
                  //   avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                  //   number: '34',
                  //   position: 5
                  // },
                  {
                    id: 4,
                    name: '邓肯',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '21',
                    position: 4
                  },
                  {
                    id: 5,
                    name: '科比',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '24',
                    position: 2
                  },
                  {
                    id: 6,
                    name: '加内特',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '5',
                    position: 4
                  },
                  {
                    id: 7,
                    name: '麦迪',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '1',
                    position: 3
                  },
                  {
                    id: 8,
                    name: '艾弗森',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '3',
                    position: 1
                  }
                ]
              },
              {
                id:2,
                teamLogo: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                teamName: '华强南湖人队',
                currentCount: 8,  // 当前报名人数
                maxCount: 30,     // 最大报名人数
                players: [
                  {
                    id: 1,
                    name: '詹姆斯',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '23',
                    position: 1
                  },
                  {
                    id: 2,
                    name: '韦德',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '3',
                    position: 2
                  },
                  {
                    id: 3,
                    name: '奥尼尔',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '34',
                    position: 5
                  },
                  {
                    id: 4,
                    name: '邓肯',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '21',
                    position: 4
                  },
                  {
                    id: 5,
                    name: '科比',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '24',
                    position: 2
                  },
                  {
                    id: 6,
                    name: '加内特',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '5',
                    position: 4
                  },
                  {
                    id: 7,
                    name: '麦迪',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '1',
                    position: 3
                  },
                  {
                    id: 8,
                    name: '艾弗森',
                    avatar: 'https://file.saidianlanqiu.com/saidian/f89f4cfcb2ba13448e9d36adb957d884a4dbb9b396da73a2351cc9ed819e5fdb.jpg',
                    number: '3',
                    position: 1
                  }
                ]
              }
            ] 
          }
        });
      }, 500);
    });
  },
  
  // 创建联赛订单
  createLeagueOrder: (data) => {
    return request({
      url: '/league/order/create',
      method: 'POST',
      data,
    });
  }
};

export default LeagueApi;
