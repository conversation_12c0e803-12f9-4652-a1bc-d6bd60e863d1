import request from '@/sheep/request';

const MessageApi = {
  // 获取未读消息数量
  getUnreadMessageCount: (params) => {
    return request({
      url: '/message/getUnreadCount',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },

  // 获取消息列表
  getMessageList: (params) => {
    return request({
      url: '/message/page',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  // 获取消息列表
  readMessage: (params) => {
    return request({
      url: '/message/read',
      method: 'PUT',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
};

export default MessageApi;
