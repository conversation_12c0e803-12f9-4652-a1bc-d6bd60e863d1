import request from '@/sheep/request';

const TeamApi = {
  // 创建球队
  createTeam: (data) => {
    return request({
      url: '/team/create',
      method: 'POST',
      custom: {
        showLoading: false,
        auth: true,
      },
      data,
    });
  },
  updateTeam: (data) => {
    return request({
      url: '/team/update',
      method: 'PUT',
      data,
      custom: {
        auth: true,
        showSuccess: true,
        successMsg: '更新成功',
      }

    });
  },
  getTeamInfo(params) {
    return request({
      url: '/team/get',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  getTeams(params) {
    return request({
      url: '/team/list',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  getTeamsMarketPage(params) {
    return request({
      url: '/team/market/page',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  getTeamOfPlayer(params) {
    return request({
      url: '/team/team-of-player',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  transferTeam(params) {
    return request({
      url: '/team/transfer',
      method: 'POST',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  removePlayer(params) {
    return request({
      url: '/team/remove-player',
      method: 'DELETE',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
};

export default TeamApi;
