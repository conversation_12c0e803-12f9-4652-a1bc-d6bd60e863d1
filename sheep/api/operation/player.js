import request from '@/sheep/request';

// 球员位置枚举
export const PLAYER_POSITIONS = {
  PG: { value: 1, label: "控球后卫" },
  SG: { value: 2, label: "得分后卫" },
  SF: { value: 3, label: "小前锋" },
  PF: { value: 4, label: "大前锋" },
  C: { value: 5, label: "中锋" }
};

const PlayerApi = {
  // 获取球员市场列表
  getPlayerMarket: (params) => {
    return request({
      url: '/league/player/market/page',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    });
  },
  // 获取球员信息
  getPlayerInfo: (params) => {
    return request({
      url: '/league/player/get',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params
    });
  },
  // 获取球员信息
  getPlayerInfoByPlayerId: (params) => {
    return request({
      url: '/league/player/info/get',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  // 修改基本信息
  updatePlayer: (data) => {
    return request({
      url: '/league/player/update',
      method: 'PUT',
      data,
      custom: {
        auth: true,
        showSuccess: true,
        successMsg: '更新成功',
      },
    });
  },

  createPlayerInfo: (data) => {
    return request({
      url: '/league/player/create',
      method: 'POST',
      data,
    });
  },
  getPlayerStatistics(params) {
    return request({
      url: '/league/player/statistics/get',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
      params,
    });
  },
  getPlayerRankPage: (params) => {
    return request({
      url: '/league/player/rank/page',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    });
  },
  getContractInfo: (params) => {
    return request({
      url: '/contract/invitation',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
        showError: false,
      },
    });
  },
  signContract: (params) => {
    return request({
      url: '/contract/player-contract/sign',
      method: 'GET',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  // 取消合同（退出球队）
  cancelContract: (params) => {
    return request({
      url: '/contract/player-contract/cancel',
      method: 'GET',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  // 解散球队
  dissolveTeam: (params) => {
    return request({
      url: '/team/delete',
      method: 'DELETE',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  // 申请入队
  applyTeam: (params) => {
    return request({
      url: '/contract/player-contract/apply',
      method: 'POST',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  // 邀请入队
  sendInvitation: (params) => {
    return request({
      url: '/contract/player-contract/invite',
      method: 'POST',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  agreeApply: (params) => {
    return request({
      url: '/contract/player-contract/agree',
      method: 'POST',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
  rejectApply: (params) => {
    return request({
      url: '/contract/player-contract/reject',
      method: 'POST',
      params,
      custom: {
        auth: true,
        showLoading: true,
        showError: true,
      },
    });
  },
};

export default PlayerApi;
