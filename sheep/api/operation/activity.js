import request from '@/sheep/request';

const ActivityApi = {
  // 分页获取活动列表
  getActivities: (params) => {
    return request({
      url: '/operation/activity/page',
      method: 'GET',
      params,
    });
  },
  
  // 分页获取活动列表 - 别名方法，与list.vue保持一致
  getActivityPage: (params) => {
    return request({
      url: '/operation/activity/page',
      method: 'GET',
      params,
    });
  },
  // 获取活动列表
  getList(params) {
    return this.getActivityPage(params);
  },

  // 获取活动详情
  getDetail: (id) => {
    return request({
      url: `/operation/activity/detail/${id}`,
      method: 'GET',
    });
  },

  // 获取活动详情 - 别名方法，确保与前端调用匹配
  getActivityDetail: (id, params = {}) => {
    return request({
      url: `/operation/activity/detail/${id}`,
      method: 'GET',
      params,
    });
  },

  // 活动报名费用结算
  settlementRegistration: (data) => {
    return request({
      url: '/operation/registration/settlement',
      method: 'POST',
      data,
    });
  },

  // 创建活动报名
  createRegistration: (data) => {
    return request({
      url: '/operation/registration/create',
      method: 'POST',
      data,
    });
  },

  // 发起好友组队报名
  createGroupRegistration: (data) => {
    return request({
      url: '/operation/registration/create-group',
      method: 'POST',
      data,
    });
  },

  // 加入好友组队报名
  joinGroupRegistration: (data) => {
    return request({
      url: '/operation/registration/join-group',
      method: 'POST',
      data,
    });
  },

  // 取消报名
  cancelRegistration: (registrationId, cancelReason) => {
    const params = {
      id: registrationId
    };
    
    // 如果有退赛原因，添加到参数中
    if (cancelReason && cancelReason.trim()) {
      params.cancelReason = cancelReason.trim();
    }
    
    return request({
      url: '/operation/registration/cancel',
      method: 'PUT',
      params: params,
    });
  },

  // 获取报名详情
  getRegistrationDetail: (registrationId) => {
    return request({
      url: '/operation/registration/get',
      method: 'GET',
      params: {
        id: registrationId
      },
    });
  },

  // 获取首页推荐活动列表 - 专用接口
  getHomeRecommendedActivities: (limit = 10) => {
    return request({
      url: '/operation/activity/home/<USER>',
      method: 'GET',
      params: {
        limit
      },
    });
  },
};

export default ActivityApi; 