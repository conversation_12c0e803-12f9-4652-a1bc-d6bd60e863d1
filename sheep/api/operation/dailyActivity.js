import request from '@/sheep/request';

const DailyActivityApi = {
  // 分页获取日常活动列表
  getDailyActivities: (params) => {
    return request({
      url: '/daily-activity/page/v2',
      method: 'GET',
      params,
    });
  },
  getDailyActivitiesInfoById: (params) => {
    return request({
      url: '/daily-activity/info',
      method: 'GET',
      params,
    });
  },

  getSettlementByActivityId: (params) => {
    return request({
      url: '/operation/daily-order/settlement',
      method: 'GET',
      params,
    });
  },

  createDailyActivityOrder: (data) => {
    return request({
      url: '/operation/daily-order/create',
      method: 'POST',
      data,
    });
  },
};


export default DailyActivityApi;
