import { defineStore } from 'pinia';
import activityApi from '@/sheep/api/operation/activity';
import signupApi from '@/sheep/api/signup';
import teamGroupApi from '@/sheep/api/teamGroup';

// 活动状态枚举
export const ACTIVITY_STATUS = {
  PREPARING: 1,    // 准备中（未开始报名）
  ENROLLING: 2,    // 报名中
  ENROLLMENT_ENDED: 3,  // 报名已截止
  ONGOING: 4,     // 进行中
  COMPLETED: 5,   // 已结束
  CANCELED: 6,    // 已取消
  GROUPING_FAILED: 7,   // 组局失败
  GROUPING_SUCCESSFUL: 8  // 组局成功
};

// 活动类型枚举
export const ACTIVITY_TYPE = {
  RANKING_MATCH: 1,  // 排位赛
  FRIENDLY_MATCH: 2, // 友谊赛
  LEAGUE: 3         // 联赛
};

// 报名模式枚举
export const SIGNUP_MODE = {
  INDIVIDUAL: 1,   // 个人报名
  FRIEND_GROUP: 2, // 好友组队
  TEAM: 3          // 球队报名
};

// 用户报名状态枚举
export const USER_SIGNUP_STATUS = {
  NOT_REGISTERED: 0,     // 未报名
  PENDING_PAYMENT: 1,    // 待支付
  PAID: 2,              // 已支付
  REGISTRATION_SUCCESS: 3, // 报名成功
  CANCELED: 4,          // 已取消
  REFUNDED: 5,          // 已退款
  FRIEND_GROUP_JOINED: 6 // 已加入好友组队
};

// 位置枚举
export const PLAYER_POSITIONS = {
  PG: 1,  // 控球后卫
  SG: 2,  // 得分后卫
  SF: 3,  // 小前锋
  PF: 4,  // 大前锋
  C: 5    // 中锋
};

// 位置选项 - 供表单使用
export const POSITION_OPTIONS = [
  { value: PLAYER_POSITIONS.PG, text: '控球后卫(PG)' },
  { value: PLAYER_POSITIONS.SG, text: '得分后卫(SG)' },
  { value: PLAYER_POSITIONS.SF, text: '小前锋(SF)' },
  { value: PLAYER_POSITIONS.PF, text: '大前锋(PF)' },
  { value: PLAYER_POSITIONS.C, text: '中锋(C)' },
];

// 获取位置名称的辅助函数
export function getPositionName(positionValue) {
  const position = POSITION_OPTIONS.find(option => option.value === positionValue);
  return position ? position.text : '未知位置';
}

export const useActivityStore = defineStore('activity', {
  state: () => ({
    activityList: [],
    currentActivityDetail: null,
    signupOptions: [],
    waitlistInfo: null,
    // 临时组队房间信息
    currentRoomInfo: null,
  }),

  getters: {
    // 活动是否已满
    isActivityFull: (state) => {
      if (!state.currentActivityDetail) return false;
      
      const { signupInfo, maxPlayersTotal } = state.currentActivityDetail;
      return signupInfo && signupInfo.currentPlayers >= maxPlayersTotal;
    },
    
    // 用户是否可以报名
    canSignup: (state) => {
      if (!state.currentActivityDetail) return false;
      
      const { signupInfo, status } = state.currentActivityDetail;
      return status === ACTIVITY_STATUS.ENROLLING && // 报名中
             signupInfo && 
             signupInfo.currentUserSignupStatus === 0; // 未报名
    },
    
    // 预估人均费用
    estimatedActivityFeePerPlayer: (state) => {
      if (!state.currentActivityDetail) return 0;
      
      return state.currentActivityDetail.signupInfo?.estimatedFeePerPlayer || 0;
    }
  },

  actions: {
    // 获取活动列表
    async fetchActivityList(params = {}) {
      try {
        const res = await activityApi.getList(params);
        if (res.code === 0 && res.data) {
          this.activityList = res.data.list || [];
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取活动列表失败', error);
        return null;
      }
    },
    
    // 获取活动详情
    async fetchActivityDetail(id) {
      try {
        const res = await activityApi.getDetail(id);
        if (res.code === 0 && res.data) {
          this.currentActivityDetail = res.data;
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取活动详情失败', error);
        return null;
      }
    },
    
    // 选择报名方式
    selectSignupMode(mode) {
      this.selectedSignupMode = mode;
    },
    
    // 提交报名
    async submitSignup(data) {
      try {
        const res = await signupApi.submit(data);
        if (res.code === 0 && res.data) {
          // 如果是好友组队发起成功，保存临时房间信息
          if (data.signupMode === 2 && res.data.roomId) {
            this.currentRoomInfo = res.data;
          }
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('提交报名失败', error);
        throw error;
      }
    },
    
    // 获取临时组队房间详情
    async fetchRoomDetail(roomId) {
      try {
        const res = await teamGroupApi.getDetail(roomId);
        if (res.code === 0 && res.data) {
          this.currentRoomInfo = res.data;
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取组队房间详情失败', error);
        return null;
      }
    },
    
    // 清除当前活动详情
    clearCurrentActivityDetail() {
      this.currentActivityDetail = null;
    },
    
    // 清除当前房间信息
    clearCurrentRoomInfo() {
      this.currentRoomInfo = null;
    }
  }
}); 