<template>
  <su-fixed
    :noFixed="props.noFixed"
    :alway="props.alway"
    :bgStyles="props.bgStyles"
    :val="0"
    :index="props.zIndex"
    noNav
    :bg="props.bg"
    :ui="props.ui"
    :opacity="props.opacity"
    :placeholder="props.placeholder"
  >
    <su-status-bar />
    <!--
      :class="[{ 'border-bottom': !props.opacity && props.bg != 'bg-none' }]"
     -->
    <view class="ui-navbar-box" :class="{ 'show-background': state.showBackground }">
      <view
        class="ui-bar ss-p-x-20"
        :class="[state.isDark ? 'text-white' : 'text-black', { 'dark-mode': state.isDark, 'light-mode': !state.isDark }]"
        :style="[{ height: sys_navBar - sys_statusBar + 'px' }]"
      >
        <view class="icon-box ss-flex">
          <view class="icon-button ss-flex ss-row-center" @tap="onClickLeft">
            <text class="sicon-back" v-if="hasHistory" />
            <text class="sicon-home" v-else />
          </view>
        </view>
        <slot name="center">
          <view class="center navbar-title">{{ title }}</view>
        </slot>
        <!-- #ifdef MP -->
        <view :style="[state.capsuleStyle]"></view>
        <!-- #endif -->
      </view>
    </view>
  </su-fixed>
</template>

<script setup>
  /**
   * 标题栏 - 基础组件navbar
   *
   * @param {Number}  zIndex = 100  							- 层级
   * @param {Boolean}  back = true 							- 是否返回上一页
   * @param {String}  backtext = ''  							- 返回文本
   * @param {String}  bg = 'bg-white'  						- 公共Class
   * @param {String}  status = ''  							- 状态栏颜色
   * @param {Boolean}  alway = true							- 是否常驻
   * @param {Boolean}  opacity = false  						- 是否开启透明渐变
   * @param {Boolean}  noFixed = false  						- 是否浮动
   * @param {String}  ui = ''									- 公共Class
   * @param {Boolean}  capsule = false  						- 是否开启胶囊返回
   * @param {Boolean}  stopBack = false 					    - 是否禁用返回
   * @param {Boolean}  placeholder = true 					- 是否开启占位
   * @param {Object}   bgStyles = {} 					    	- 背景样式
   *
   */

  import { computed, reactive, onBeforeMount, ref } from 'vue';
  import sheep from '@/sheep';
  import { onPageScroll } from '@dcloudio/uni-app';
  import { showMenuTools, closeMenuTools } from '@/sheep/hooks/useModal';

  // 本地数据
  const state = reactive({
    statusCur: '',
    capsuleStyle: {},
    capsuleBack: {},
    isDark: true,
    showBackground: false, // 新增：控制背景色显示
  });

  const sys_statusBar = sheep.$platform.device.statusBarHeight;
  const sys_navBar = sheep.$platform.navbar;

  const props = defineProps({
    zIndex: {
      type: Number,
      default: 100,
    },

    title: {
      //返回文本
      type: String,
      default: '',
    },
    bg: {
      type: String,
      default: '',
    },
    // 常驻
    alway: {
      type: Boolean,
      default: true,
    },
    opacity: {
      //是否开启滑动渐变
      type: Boolean,
      default: true,
    },
    noFixed: {
      //是否浮动
      type: Boolean,
      default: false,
    },
    ui: {
      type: String,
      default: '',
    },
    capsule: {
      //是否开启胶囊返回
      type: Boolean,
      default: false,
    },
    stopBack: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: [Boolean],
      default: false,
    },
    bgStyles: {
      type: Object,
      default() {},
    },
  });

  const emits = defineEmits(['navback', 'clickLeft']);
  const hasHistory = sheep.$router.hasHistory();

  onBeforeMount(() => {
    init();
  });

  onPageScroll((e) => {
    let top = e.scrollTop;
    // 滚动超过状态栏高度时显示白色背景
    const threshold = sys_statusBar + 20; // 状态栏高度 + 20rpx缓冲
    state.isDark = top < threshold;
    state.showBackground = top >= threshold;
  });

  function onClickLeft() {
    if (hasHistory) {
      sheep.$router.back();
    } else {
      sheep.$router.go('/pages/index/index');
    }
    emits('clickLeft');
  }

  // 初始化
  const init = () => {
    // #ifdef MP-ALIPAY
    my.hideAllFavoriteMenu();
    // #endif
    state.capsuleStyle = {
      width: sheep.$platform.capsule.width + 'px',
      height: sheep.$platform.capsule.height + 'px',
    };

    state.capsuleBack = state.capsuleStyle;
  };
</script>

<style lang="scss" scoped>
  .icon-box {
    width: 60rpx;
    height: 60rpx;
    
    .sicon-back {
      font-size: 48rpx;
      color: #fff;
      transition: color 0.3s ease;
    }
    .sicon-home {
      font-size: 48rpx;
      color: #fff;
      transition: color 0.3s ease;
    }
    
    .icon-button {
      width: 60rpx;
      height: 60rpx;
      border-radius: 30rpx;
      transition: background-color 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  // 深色模式（透明背景时）
  .ui-bar.dark-mode {
    .sicon-back,
    .sicon-home {
      color: #fff;
    }
    
    .icon-button:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  // 浅色模式（白色背景时）
  .ui-bar.light-mode {
    .sicon-back,
    .sicon-home {
      color: #333;
    }
    
    .icon-button:hover {
      background: rgba(0, 0, 0, 0.1);
    }
  }
  .navbar-title {
    font-size: 36rpx;
  }
  .tools-icon {
    font-size: 40rpx;
  }
  .ui-navbar-box {
    background-color: transparent;
    width: 100%;
    transition: background-color 0.3s ease;
    
    &.show-background {
      background-color: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
    }

    .ui-bar {
      position: relative;
      z-index: 2;
      white-space: nowrap;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: space-between;

      .left {
        @include flex-bar;

        .back {
          @include flex-bar;

          .back-icon {
            @include flex-center;
            width: 56rpx;
            height: 56rpx;
            margin: 0 10rpx;
            font-size: 46rpx !important;

            &.opacityIcon {
              position: relative;
              border-radius: 50%;
              background-color: rgba(127, 127, 127, 0.5);

              &::after {
                content: '';
                display: block;
                position: absolute;
                height: 200%;
                width: 200%;
                left: 0;
                top: 0;
                border-radius: inherit;
                transform: scale(0.5);
                transform-origin: 0 0;
                opacity: 0.1;
                border: 1px solid currentColor;
                pointer-events: none;
              }

              &::before {
                transform: scale(0.9);
              }
            }
          }

          /* #ifdef  MP-ALIPAY */
          ._icon-back {
            opacity: 0;
          }

          /* #endif */
        }

        .capsule {
          @include flex-bar;
          border-radius: 100px;
          position: relative;

          &.dark {
            background-color: rgba(255, 255, 255, 0.5);
          }

          &.light {
            background-color: rgba(0, 0, 0, 0.15);
          }

          &::after {
            content: '';
            display: block;
            position: absolute;
            height: 60%;
            width: 1px;
            left: 50%;
            top: 20%;
            background-color: currentColor;
            opacity: 0.1;
            pointer-events: none;
          }

          &::before {
            content: '';
            display: block;
            position: absolute;
            height: 200%;
            width: 200%;
            left: 0;
            top: 0;
            border-radius: inherit;
            transform: scale(0.5);
            transform-origin: 0 0;
            opacity: 0.1;
            border: 1px solid currentColor;
            pointer-events: none;
          }

          .capsule-back,
          .capsule-home {
            @include flex-center;
            flex: 1;
          }

          &.isFristPage {
            .capsule-back,
            &::after {
              display: none;
            }
          }
        }
      }

      .right {
        @include flex-bar;

        .right-content {
          @include flex;
          flex-direction: row-reverse;
        }
      }

      .center {
        @include flex-center;
        text-overflow: ellipsis;
        // text-align: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

        .image {
          display: block;
          height: 36px;
          max-width: calc(100vw - 200px);
        }
      }
    }

    .ui-bar-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      z-index: 1;
      pointer-events: none;
    }
  }
</style>
