import { ref, computed, reactive, watch } from 'vue';
import { ACTIVITY_TYPE, SIGNUP_MODE } from '@/sheep/store/activity';
import ActivityApi from '@/sheep/api/operation/activity';
import sheep from '@/sheep';
import dayjs from 'dayjs';

/**
 * 报名确认页业务逻辑Composable
 * @param {Object} routeParams 路由参数
 * @returns {Object} 页面所需的状态和方法
 */
export default function useConfirmOrder(routeParams) {
  console.log('[useConfirmOrder] Initialized with params:', routeParams);
  
  // 基础状态
  const loading = ref(true);
  const submitting = ref(false);
  const agreementChecked = ref(false);
  const showRiskNotification = ref(false);
  
  // 数据状态
  const activity = ref(null);
  const teamInfo = ref(null);
  const friendGroupInfo = ref(null);
  const couponInfo = ref([]);
  const selectedCouponId = ref(null);
  const isUsingPoints = ref(false);
  const teamPaymentMethod = ref('team_total');
  
  // 球员档案信息
  const playerProfileInfo = reactive({
    isNewUser: false,
    playerId: null,
    playerName: '',
    playerAvatar: '',
    jerseyNumber: '',
    sex: null,
    position: null,
    height: null,
    weight: null
  });
  
  // 新用户信息
  const newPlayerProfile = ref({
    avatar: '',
    name: '',
    jerseyNumber: '',
    sex: '1',
    position: null,
    height: '',
    weight: ''
  });
  
  // 监听 newPlayerProfile 的变化
  watch(newPlayerProfile, (newValue) => {
    console.log('[useConfirmOrder] newPlayerProfile changed in hook:', JSON.stringify(newValue));
  }, { deep: true });
  
  // 费用信息
  const feeInfo = reactive({
    totalAmount: 0,
    payAmount: 0,
    couponDiscountAmount: 0,
    pointsDiscountAmount: 0,
    estimatedFeePerPlayer: 0,
  });
  
  // 用户积分信息
  const userPointsInfo = reactive({
    availablePoints: 0,
    pointsToCashRate: 0,
    maxPointsUsableForOrder: 0,
    maxPointsDiscountAmount: 0,
  });
  
  // 活动费用信息
  const activityFeeInfo = reactive({
    venueFeeAmount: 0,
    serviceFeeAmount: 0,
    maxPlayersConfig: 0,
    minPlayersConfig: 0,
    activityType: 0,
    estimatedFeePerPlayer: 0,
    friendlyTeamFee: 0,
    friendlyAAFee: 0
  });
  
  // 计算属性
  const isReady = computed(() => {
    return !loading.value && activity.value && activity.value.id && feeInfo.payAmount !== undefined;
  });
  
  const shouldShowPaymentOptions = computed(() => {
    const activityType = Number(routeParams.activityType);
    const signupMode = Number(routeParams.signupMode);
    
    // 友谊赛球队报名时显示支付方式选择
    if (activityType === ACTIVITY_TYPE.FRIENDLY_MATCH && signupMode === SIGNUP_MODE.TEAM) {
      return true;
    }
    
    // 联赛球队报名时显示支付方式（固定个人费用）
    if (activityType === ACTIVITY_TYPE.LEAGUE && signupMode === SIGNUP_MODE.TEAM) {
      return true;
    }
    
    return false;
  });
  
  const shouldShowFriendRoomInfo = computed(() => {
    return Number(routeParams.activityType) === ACTIVITY_TYPE.RANKING_MATCH && 
           Number(routeParams.signupMode) === 2 && // 好友组队模式
           routeParams.roomId;
  });
  
  const shouldShowTeamInfo = computed(() => {
    const activityType = Number(routeParams.activityType);
    const signupMode = Number(routeParams.signupMode);
    
    return signupMode === SIGNUP_MODE.TEAM && 
           (activityType === ACTIVITY_TYPE.FRIENDLY_MATCH || activityType === ACTIVITY_TYPE.LEAGUE);
  });
  
  const isNewProfileComplete = computed(() => {
    if (!playerProfileInfo.isNewUser) return true;
    const profile = newPlayerProfile.value;
    const complete = !!(profile.name && profile.jerseyNumber && profile.position && profile.height && profile.weight);
    console.log('[useConfirmOrder] isNewProfileComplete:', complete, 'profile:', {
      name: profile.name,
      jerseyNumber: profile.jerseyNumber,
      position: profile.position,
      height: profile.height,
      weight: profile.weight
    });
    return complete;
  });
  
  const canSubmit = computed(() => {
    const ready = isReady.value;
    const agreed = agreementChecked.value;
    const notSubmitting = !submitting.value;
    const profileComplete = isNewProfileComplete.value;
    const result = ready && agreed && notSubmitting && profileComplete;
    
    console.log('[useConfirmOrder] canSubmit calculation:', {
      result,
      isReady: ready,
      agreementChecked: agreed,
      notSubmitting: notSubmitting,
      isNewProfileComplete: profileComplete,
      'profile data': {
        isNewUser: playerProfileInfo.isNewUser,
        newPlayerProfile: JSON.stringify(newPlayerProfile.value)
      }
    });
    return result;
  });
  
  const feeDescriptionText = computed(() => {
    const activityType = Number(routeParams.activityType);
    const signupMode = Number(routeParams.signupMode);
    
    if (activityType === ACTIVITY_TYPE.RANKING_MATCH) {
      // 排位赛：按最小人数平摊，多退少补
      const minPlayers = activityFeeInfo.minPlayersConfig || 10;
      return `球员个人支付（${minPlayers}人平摊）`;
    } else if (activityType === ACTIVITY_TYPE.FRIENDLY_MATCH) {
      if (signupMode === SIGNUP_MODE.TEAM) {
        if (teamPaymentMethod.value === 'team_total') {
          return '队长垫付全队费用';
        } else {
          // AA制，根据队伍最小人数计算
          const minPlayers = activityFeeInfo.minPlayersConfig || 5;
          return `队员AA支付（预估${minPlayers}人平摊）`;
        }
      }
      return '球员个人支付';
    } else if (activityType === ACTIVITY_TYPE.LEAGUE) {
      return '联赛个人报名费';
    }
    
    return '球员个人支付';
  });
  
  // 初始化页面数据
  const init = async () => {
    console.log('[useConfirmOrder] init called');
    loading.value = true;
    
    try {
      const settlementReqData = {
        activityId: Number(routeParams.activityId),
        activityType: Number(routeParams.activityType),
        signupMode: Number(routeParams.signupMode),
      };

      // 添加特定参数
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH && Number(routeParams.signupMode) === SIGNUP_MODE.TEAM) {
        if (routeParams.teamId) {
          settlementReqData.teamId = routeParams.teamId;
          
          // 设置球队信息到teamInfo中
          teamInfo.value = {
            teamId: routeParams.teamId,
            name: routeParams.teamName || '选中的球队',
            logo: routeParams.teamLogo || '',
            // 可以从API获取更详细的球队信息，这里先用基本信息
          };
          console.log('[useConfirmOrder] 友谊赛 - 设置球队信息:', teamInfo.value);
        }
        settlementReqData.paymentType = teamPaymentMethod.value === 'team_total' ? 1 : 2;
      }

      // 联赛也需要处理球队信息
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.LEAGUE && Number(routeParams.signupMode) === SIGNUP_MODE.TEAM) {
        if (routeParams.teamId) {
          settlementReqData.teamId = routeParams.teamId;
          
          // 设置球队信息到teamInfo中
          teamInfo.value = {
            teamId: routeParams.teamId,
            name: routeParams.teamName || '选中的球队',
            logo: routeParams.teamLogo || '',
          };
          console.log('[useConfirmOrder] 联赛 - 设置球队信息:', teamInfo.value);
        }
      }

      if (routeParams.roomId) {
        settlementReqData.roomId = routeParams.roomId;
      }
      
      console.log('[useConfirmOrder] Calling settlementRegistration with data:', settlementReqData);
      const response = await ActivityApi.settlementRegistration(settlementReqData);
      console.log('[useConfirmOrder] settlementRegistration response:', response);
      
      if (!response || response.code !== 0) {
        console.error('[useConfirmOrder] API调用失败:', response);
        // 使用模拟数据作为fallback
        await loadMockData();
        return;
      }
      
      if (!response.data) {
        console.error('[useConfirmOrder] API返回数据为空');
        await loadMockData();
        return;
      }

      // 处理成功响应数据
      const data = response.data;
      await processSettlementData(data);
      
    } catch (error) {
      console.error('[useConfirmOrder] 初始化失败', error);
      await loadMockData();
      uni.showToast({
        title: error.message || '加载订单数据失败，使用模拟数据',
        icon: 'none'
      });
    } finally {
      loading.value = false;
    }
  };
  
  // 处理结算数据
  const processSettlementData = (data) => {
    console.log('[useConfirmOrder] Processing settlement data:', data);
    
    // 处理球员档案信息
    if (data.playerProfile) {
      Object.assign(playerProfileInfo, {
        isNewUser: Boolean(routeParams.isNewUser) || Boolean(data.playerProfile.isNewUser),
        playerId: data.playerProfile.playerId,
        playerName: data.playerProfile.playerName || '',
        playerAvatar: data.playerProfile.playerAvatar || '',
        jerseyNumber: data.playerProfile.jerseyNumber || '',
        sex: data.playerProfile.sex,
        position: data.playerProfile.position,
        height: data.playerProfile.height,
        weight: data.playerProfile.weight
      });
    } else {
      playerProfileInfo.isNewUser = Boolean(routeParams.isNewUser);
    }
    
    // 处理活动信息
    if (data.activityInfo) {
      activity.value = { ...data.activityInfo };
      // 格式化时间
      if (data.activityInfo.startTime) {
        let timestamp = data.activityInfo.startTime;
        if (timestamp.toString().length === 10) {
          timestamp = timestamp * 1000;
        }
        activity.value.startTime = dayjs(timestamp).format('YYYY-MM-DD HH:mm');
        activity.value.activityTimeDisplay = dayjs(timestamp).format('YYYY-MM-DD HH:mm');
      }
    } else {
      // 创建基础活动对象
      activity.value = {
        id: routeParams.activityId,
        title: '活动加载中...',
        activityType: Number(routeParams.activityType),
        startTime: '待确认',
        location: '待确认'
      };
    }

    // 处理费用信息
    if (data.feeInfo) {
      Object.assign(feeInfo, {
        totalAmount: data.feeInfo.totalAmount || 0,
        payAmount: data.feeInfo.payAmount || 0,
        couponDiscountAmount: data.feeInfo.couponDiscountAmount || 0,
        pointsDiscountAmount: data.feeInfo.pointsDiscountAmount || 0,
        estimatedFeePerPlayer: data.feeInfo.estimatedFeePerPlayer || 0
      });
    }

    // 处理优惠券信息
    couponInfo.value = (data.availableCoupons || []).map(coupon => ({
      ...coupon,
      match: true,
      mismatchReason: null
    }));

    // 处理用户积分信息
    if (data.userPointsInfo) {
      Object.assign(userPointsInfo, {
        availablePoints: data.userPointsInfo.availablePoints || 0,
        pointsToCashRate: data.userPointsInfo.pointsToCashRate || 0,
        maxPointsUsableForOrder: data.userPointsInfo.maxPointsUsableForOrder || 0,
        maxPointsDiscountAmount: data.userPointsInfo.maxPointsDiscountAmount || 0
      });
    }
    
    // 处理活动费用信息
    if (data.activityOriginalFee) {
      Object.assign(activityFeeInfo, {
        venueFeeAmount: data.activityOriginalFee.venueFeeAmount || 0,
        serviceFeeAmount: data.activityOriginalFee.serviceFeeAmount || 0,
        maxPlayersConfig: data.activityOriginalFee.maxPlayersConfig || 0,
        minPlayersConfig: data.activityOriginalFee.minPlayersConfig || 0,
        activityType: data.activityOriginalFee.activityType || 0,
        estimatedFeePerPlayer: data.activityOriginalFee.estimatedFeePerPlayer || 0,
        friendlyTeamFee: data.activityOriginalFee.friendlyTeamFee || 0,
        friendlyAAFee: data.activityOriginalFee.friendlyAAFee || 0,
        teamPaymentType: data.activityOriginalFee.teamPaymentType || null
      });
      
      // 如果是友谊赛且球队支付方式已固定，自动设置对应的支付方式
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH && 
          data.activityOriginalFee.teamPaymentType) {
        const fixedPaymentType = data.activityOriginalFee.teamPaymentType;
        if (fixedPaymentType === 1) {
          teamPaymentMethod.value = 'team_total';
        } else if (fixedPaymentType === 2) {
          teamPaymentMethod.value = 'team_aa';
        }
        console.log('[useConfirmOrder] 球队支付方式已固定，自动设置为:', teamPaymentMethod.value);
      }
    }
    
    // 处理好友组队信息（如果是好友组队报名模式）
    if (Number(routeParams.signupMode) === SIGNUP_MODE.FRIEND_GROUP && data.friendGroupInfo) {
      console.log('[useConfirmOrder] 处理好友组队信息:', data.friendGroupInfo);
      friendGroupInfo.value = {
        ...data.friendGroupInfo,
        // 确保数据转换正确
        owner: data.friendGroupInfo.owner || {
          userId: data.friendGroupInfo.ownerUserId,
          nickname: '房主',
          avatar: null
        },
        members: data.friendGroupInfo.members || [],
        maxMembers: data.friendGroupInfo.maxMembers || 3,
        expireTime: data.friendGroupInfo.expireTime || null
      };
    }
  };
  
  // 加载模拟数据作为fallback
  const loadMockData = async () => {
    console.log('[useConfirmOrder] Loading mock data as fallback');
    
    // 模拟活动信息
    activity.value = {
      id: routeParams.activityId,
      title: '香蜜湖赛区 - 排位赛',
      activityType: Number(routeParams.activityType),
      startTime: '2024-03-27 19:30',
      location: '深圳市南山区香蜜湖体育中心',
      description: '欢迎参加排位赛活动'
    };
    
    // 模拟费用信息
    Object.assign(feeInfo, {
      totalAmount: 2000, // 20元
      payAmount: 2000,
      couponDiscountAmount: 0,
      pointsDiscountAmount: 0,
      estimatedFeePerPlayer: 1000 // 10元
    });
    
    // 模拟球员档案
    playerProfileInfo.isNewUser = Boolean(routeParams.isNewUser);
    
    // 模拟用户积分
    Object.assign(userPointsInfo, {
      availablePoints: 1000,
      pointsToCashRate: 100,
      maxPointsUsableForOrder: 500,
      maxPointsDiscountAmount: 500
    });
    
    // 模拟活动费用配置
    Object.assign(activityFeeInfo, {
      venueFeeAmount: 1800,
      serviceFeeAmount: 200,
      maxPlayersConfig: 16,
      minPlayersConfig: 8,
      activityType: Number(routeParams.activityType),
      estimatedFeePerPlayer: 1000
    });
    
    console.log('[useConfirmOrder] Mock data loaded successfully');
  };
  
  // 重新计算订单价格
  const recalculateOrderPrice = async (type, value) => {
    console.log('[useConfirmOrder] recalculateOrderPrice called:', { type, value });
    
    if (type === 'coupon') {
      selectedCouponId.value = value;
    } else if (type === 'points') {
      isUsingPoints.value = value;
    }
    
    try {
      const settlementData = {
        activityId: Number(routeParams.activityId),
        activityType: Number(routeParams.activityType),
        signupMode: Number(routeParams.signupMode),
        couponId: selectedCouponId.value,
        usePoints: isUsingPoints.value,
        pointsToUse: isUsingPoints.value ? userPointsInfo.maxPointsUsableForOrder : 0
      };

      // 添加特定参数
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH && Number(routeParams.signupMode) === SIGNUP_MODE.TEAM) {
        if (routeParams.teamId) {
          settlementData.teamId = Number(routeParams.teamId);
        }
        settlementData.paymentType = teamPaymentMethod.value === 'team_total' ? 1 : 2;
      } else if (Number(routeParams.activityType) === ACTIVITY_TYPE.LEAGUE && Number(routeParams.signupMode) === SIGNUP_MODE.TEAM) {
        if (routeParams.teamId) {
          settlementData.teamId = Number(routeParams.teamId);
        }
      }

      if (routeParams.roomId) {
        settlementData.roomId = routeParams.roomId;
      }

      console.log('[useConfirmOrder] 重新调用结算接口，参数:', settlementData);

      const response = await ActivityApi.settlementRegistration(settlementData);
      
      if (response.code === 0) {
        const data = response.data;
        
        // 更新费用信息
        if (data.feeInfo) {
          Object.assign(feeInfo, {
            totalAmount: data.feeInfo.totalAmount || 0,
            payAmount: data.feeInfo.payAmount || 0,
            couponDiscountAmount: data.feeInfo.couponDiscountAmount || 0,
            pointsDiscountAmount: data.feeInfo.pointsDiscountAmount || 0,
            estimatedFeePerPlayer: data.feeInfo.estimatedFeePerPlayer || 0
          });
        }
        
        // 更新优惠券信息
        if (data.availableCoupons) {
          couponInfo.value = data.availableCoupons.map(coupon => ({
            ...coupon,
            match: true,
            mismatchReason: null
          }));
        }
        
        // 更新活动费用信息（包括友谊赛固定费用）
        if (data.activityOriginalFee) {
          Object.assign(activityFeeInfo, {
            venueFeeAmount: data.activityOriginalFee.venueFeeAmount || 0,
            serviceFeeAmount: data.activityOriginalFee.serviceFeeAmount || 0,
            maxPlayersConfig: data.activityOriginalFee.maxPlayersConfig || 0,
            minPlayersConfig: data.activityOriginalFee.minPlayersConfig || 0,
            activityType: data.activityOriginalFee.activityType || 0,
            estimatedFeePerPlayer: data.activityOriginalFee.estimatedFeePerPlayer || 0,
            friendlyTeamFee: data.activityOriginalFee.friendlyTeamFee || 0,
            friendlyAAFee: data.activityOriginalFee.friendlyAAFee || 0
          });
        }

        console.log('[useConfirmOrder] 费用重新计算完成:', JSON.parse(JSON.stringify(feeInfo)));
      } else {
        console.error('[useConfirmOrder] 重新计算费用失败:', response.msg);
        uni.showToast({ title: '计算费用失败', icon: 'none' });
      }
    } catch (error) {
      console.error('[useConfirmOrder] 重新计算费用异常:', error);
      uni.showToast({ title: '计算费用失败，请重试', icon: 'none' });
    }
  };
  
  // 提交订单
  const handleSubmit = async () => {
    console.log('[useConfirmOrder] handleSubmit called');
    
    if (!agreementChecked.value) {
      showRiskNotification.value = true;
      return;
    }

    if (!canSubmit.value) {
      if (playerProfileInfo.isNewUser && !isNewProfileComplete.value) {
        uni.showToast({ title: '请完善球员信息', icon: 'none' });
      } else if (!agreementChecked.value) {
        uni.showToast({ title: '请先同意协议', icon: 'none' });
      } else {
        uni.showToast({ title: '请稍候或检查信息', icon: 'none' });
      }
      return;
    }
    
    submitting.value = true;
    
    try {
      // 在提交前，如果有支付方式选择，先重新计算费用以确保金额准确
      if (shouldShowPaymentOptions.value && Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH) {
        console.log('[useConfirmOrder] 提交前重新计算费用，支付方式:', teamPaymentMethod.value);
        await recalculateOrderPrice('payment', teamPaymentMethod.value);
      }

      const orderSubmitData = {
        activityId: Number(routeParams.activityId),
        originalPrice: feeInfo.totalAmount,
        couponId: selectedCouponId.value,
        usePoints: isUsingPoints.value,
        pointsToUse: isUsingPoints.value ? userPointsInfo.maxPointsUsableForOrder : 0
      };

      // 只有新用户才传递球员档案信息
      if (playerProfileInfo.isNewUser) {
        orderSubmitData.playerProfile = {
                  avatar: newPlayerProfile.value.avatar,
        name: newPlayerProfile.value.name,
        jerseyNumber: newPlayerProfile.value.jerseyNumber,
        sex: Number(newPlayerProfile.value.sex),
        position: newPlayerProfile.value.position,
        height: Number(newPlayerProfile.value.height),
        weight: Number(newPlayerProfile.value.weight)
        };
      }
      // 如果不是新用户，不传递playerProfile字段，让后台直接使用现有球员信息

      // 添加特定参数
      if (shouldShowPaymentOptions.value) {
        if (Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH) {
          orderSubmitData.paymentType = teamPaymentMethod.value === 'team_total' ? 1 : 2;
          orderSubmitData.teamId = routeParams.teamId ? Number(routeParams.teamId) : undefined;
        } else if (Number(routeParams.activityType) === ACTIVITY_TYPE.LEAGUE) {
          orderSubmitData.teamId = routeParams.teamId ? Number(routeParams.teamId) : undefined;
        }
      }

      // 修复好友组队参数传递逻辑
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.RANKING_MATCH) {
        const signupMode = Number(routeParams.signupMode);
        orderSubmitData.signupMode = signupMode;
        
        // 如果有邀请码，说明是加入好友组
        if (routeParams.invite_code) {
          orderSubmitData.inviteCode = routeParams.invite_code;
          orderSubmitData.isGroupLeader = false;
          console.log('[useConfirmOrder] 加入好友组，邀请码:', routeParams.invite_code);
        } else if (signupMode === 2) {
          // 好友组队模式：2代表好友组队，1代表个人报名
          orderSubmitData.isGroupLeader = true;
          console.log('[useConfirmOrder] 设置为好友组队发起人');
        } else {
          orderSubmitData.isGroupLeader = false;
          console.log('[useConfirmOrder] 设置为个人报名');
        }
      }

      // 移除重复的roomId逻辑，因为上面已经处理了邀请码情况
      
      console.log('[useConfirmOrder] 提交订单数据:', orderSubmitData);

      // 根据报名模式调用不同的API接口
      let response;
      if (Number(routeParams.activityType) === ACTIVITY_TYPE.RANKING_MATCH && routeParams.invite_code) {
        // 加入好友组队
        response = await ActivityApi.joinGroupRegistration({
          createReqVO: orderSubmitData,
          invitationCode: routeParams.invite_code
        });
        console.log('[useConfirmOrder] 调用加入好友组接口');
      } else if (Number(routeParams.activityType) === ACTIVITY_TYPE.RANKING_MATCH && Number(routeParams.signupMode) === 2) {
        // 发起好友组队
        response = await ActivityApi.createGroupRegistration(orderSubmitData);
        console.log('[useConfirmOrder] 调用发起好友组接口');
      } else {
        // 普通报名
        response = await ActivityApi.createRegistration(orderSubmitData);
        console.log('[useConfirmOrder] 调用普通报名接口');
      }
      
      if (response.code !== 0) {
        throw new Error(response.msg || '提交订单失败');
      }

      const data = response.data;
      console.log('[useConfirmOrder] 订单创建成功:', data);

      if (data.payOrderId && data.payOrderId > 0) {
        // 需要支付
        uni.showToast({ title: '订单提交成功', icon: 'success' });
        sheep.$router.go('/pages/pay/index', {
          id: data.payOrderId,
          orderType: 'activity',
        });
      } else {
        // 0元支付
        uni.showToast({ title: '报名成功！', icon: 'success' });
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/activity/detail?id=${routeParams.activityId}`
          });
        }, 1500);
      }

    } catch (error) {
      console.error('[useConfirmOrder] 提交订单失败', error);
      uni.showToast({
        title: error.message || '提交订单失败，请重试',
        icon: 'none'
      });
    } finally {
      submitting.value = false;
    }
  };
  
  // 选择球队支付方式
  const selectTeamPaymentMethod = (method) => {
    console.log('[useConfirmOrder] selectTeamPaymentMethod:', method);
    teamPaymentMethod.value = method;
    // 重新计算费用，确保获取最新的费用信息
    recalculateOrderPrice('payment', method);
  };
  
  // 处理积分使用切换
  const handleTogglePoints = (usePoints) => {
    console.log('[useConfirmOrder] handleTogglePoints:', usePoints);
    // 立即更新本地状态
    isUsingPoints.value = usePoints;
    // 重新计算费用
    recalculateOrderPrice('points', usePoints);
  };
  
  // 选择球队
  const selectTeam = (team) => {
    console.log('[useConfirmOrder] selectTeam:', team);
    // 跳转到球队选择页面
    uni.navigateTo({
      url: `/pages/team/select?activityId=${routeParams.activityId}&activityType=${routeParams.activityType}`
    });
  };
  
  // 获取费用说明的支付方式
  const getPaymentTypeForFeeExplanation = () => {
    if (shouldShowPaymentOptions.value && Number(routeParams.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH) {
      return teamPaymentMethod.value === 'team_total' ? 1 : 2;
    }
    return null;
  };
  
  // 处理球员档案更新
  const handleProfileUpdate = (updatedProfile) => {
    console.log('[useConfirmOrder] handleProfileUpdate:', updatedProfile);
    // 直接赋值给ref的value
    newPlayerProfile.value = { ...updatedProfile };
  };
  
  // 处理协议勾选变化
  const handleAgreementChange = (isChecked) => {
    console.log('[useConfirmOrder] handleAgreementChange:', isChecked, 'current agreementChecked:', agreementChecked.value);
    if (isChecked) {
      // 勾选时立即弹出协议弹窗，但不设置为true
      if (!agreementChecked.value) {
        openAgreementModal();
      }
    } else {
      // 取消勾选
      agreementChecked.value = false;
    }
  };
  
  // 兼容旧的处理方式
  const handleCheckboxChange = (e) => {
    const isChecked = e.detail.value.length > 0;
    handleAgreementChange(isChecked);
  };
  
  // 打开协议弹窗
  const openAgreementModal = () => {
    showRiskNotification.value = true;
  };
  
  // 确认阅读协议
  const onReadRiskNotification = () => {
    console.log('[useConfirmOrder] User confirmed risk notification');
    agreementChecked.value = true;
    showRiskNotification.value = false;
  };
  
  // 关闭协议弹窗
  const closeRiskNotification = () => {
    console.log('[useConfirmOrder] Risk notification closed, agreementChecked:', agreementChecked.value);
    showRiskNotification.value = false;
    // 如果用户没有确认协议就关闭弹窗，确保协议状态为false
    if (!agreementChecked.value) {
      console.log('[useConfirmOrder] User closed without confirming, ensuring agreement is unchecked');
      // 这里可能需要强制更新UI
    }
  };
  
  // 返回所有状态和方法
  return {
    // 状态
    loading,
    submitting,
    agreementChecked,
    showRiskNotification,
    activity,
    teamInfo,
    friendGroupInfo,
    couponInfo,
    selectedCouponId,
    isUsingPoints,
    teamPaymentMethod,
    playerProfileInfo,
    newPlayerProfile,
    feeInfo,
    userPointsInfo,
    activityFeeInfo,
    
    // 计算属性
    isReady,
    shouldShowPaymentOptions,
    shouldShowFriendRoomInfo,
    shouldShowTeamInfo,
    isNewProfileComplete,
    canSubmit,
    feeDescriptionText,
    
    // 方法
    init,
    recalculateOrderPrice,
    handleSubmit,
    selectTeamPaymentMethod,
    handleTogglePoints,
    selectTeam,
    getPaymentTypeForFeeExplanation,
    handleProfileUpdate,
    handleAgreementChange,
    handleCheckboxChange,
    openAgreementModal,
    onReadRiskNotification,
    closeRiskNotification
  };
} 