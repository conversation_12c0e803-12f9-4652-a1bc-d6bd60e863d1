// ========== 日常活动相关的工具函数 ==========

import dayjs from 'dayjs';

export const getPosition = (position) => {
  if (position === 1) {
    return '控球后卫';
  } else if (position === 2) {
    return '得分后卫';
  } else if (position === 3) {
    return '小前锋';
  } else if (position === 4) {
    return '大前锋';
  } else if (position === 5) {
    return '中锋';
  }
};
export const getDailyActivityMode = (dailyActivityInfo) => {
  if (dailyActivityInfo.mode === 1) {
    return '计时制 | 100分钟';
  } else if (dailyActivityInfo.mode === 2) {
    return '计分制 | 120分';
  } else {
    return '其它';
  }
};

export const getDailyActivityType = (type) => {
  if (type === 1) {
    return '全场5人制篮球赛';
  } else if (type === 2) {
    return '半场4人制篮球赛';
  } else {
    return '其他';
  }
};

export const formatDailyActivityOrderStartTime = (startTime) => {
  return dayjs(startTime).format('YYYY-MM-DD ' + getWeekDay(startTime) + ' HH:mm:ss');
};

export const getWeekDay = (startTime) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  return '周' + week[dayjs(startTime).day()];
};


/**
 * 距离比赛开始剩余小于24小时,返回true
 * 如果比赛已经开始,返回false
 * @param startTime
 */
export const isGameReadyToStart = (startTime) => {
  const now = dayjs();
  const start = dayjs(startTime);
  if (now >= start) {
    return false;
  }

  let diff = start.diff(now, 'hour');
  return diff < 24;
};
/**
 * 距离比赛开始剩余小于24小时,返回true
 * 如果比赛已经开始,返回false
 * @param startTime
 */
export const isGameExpired = (startTime) => {
  const now = dayjs();
  const start = dayjs(startTime);
  return now >= start;
};

export const status =
  [
    { status: 1, text: '报名中' },
    { status: 2, text: '待开始' },
    { status: 3, text: '进行中' },
    { status: 4, text: '已结束' },
    { status: 5, text: '已取消' },
  ];

/**
 * 当前活动是否无效
 * 活动处于进行中、已结束、已取消为无效状态
 * @param startTime
 */
export const isDailyActivityInValid = (status) => {
  const inValidStatus =
    [
      { status: 3, text: '进行中' },
      { status: 4, text: '已结束' },
      { status: 5, text: '已取消' },
    ];
  return inValidStatus.some(item => item.status === status);
};
/**
 * 当前活动是否无效
 * 活动处于进行中、已结束、已取消为无效状态
 * @param startTime
 */
export const isDailyActivityValid = (status) => {
  const inValidStatus =
    [
      { status: 1, text: '报名中' },
      { status: 2, text: '待开始' },
    ];
  return inValidStatus.some(item => item.status === status);
};

export const getGameStatus = (status) => {
  const gameStatus =
    [
      { status: 1, text: '待开始' },
      { status: 2, text: '待开始' },
      { status: 3, text: '进行中' },
      { status: 4, text: '已结束' },
      { status: 5, text: '已取消' },
    ];
  let find = gameStatus.find(item => item.status === status);
  return find != null ? find.text : '未知';
};

export const getGameType = (type) => {
  const gameType =
    [
      { type: 1, text: '日常活动' },
    ];
  let find = gameType.find(item => item.type === type);
  return find != null ? find.text : '未知';
};

