// ========== COMMON - 公共模块 ==========

/**
 * 与后端Terminal枚举一一对应
 */
export const TerminalEnum = {
  UNKNOWN: 0, // 未知, 目的：在无法解析到 terminal 时，使用它
  WECHAT_MINI_PROGRAM: 10, //微信小程序
  WECHAT_WAP: 11, // 微信公众号
  H5: 20, // H5 网页
  APP: 31, // 手机 App
};

/**
 * 将 uni-app 提供的平台转换为后端所需的 terminal值
 *
 * @return 终端
 */
export const getTerminal = () => {
  const platformType = uni.getSystemInfoSync().uniPlatform;
  // 与后端terminal枚举一一对应
  switch (platformType) {
    case 'app':
      return TerminalEnum.APP;
    case 'web':
      return TerminalEnum.H5;
    case 'mp-weixin':
      return TerminalEnum.WECHAT_MINI_PROGRAM;
    default:
      return TerminalEnum.UNKNOWN;
  }
};

// ========== MALL - 营销模块 ==========

import dayjs from 'dayjs';

/**
 * 比赛活动的状态枚举
 */
export const DailyActivityStatus = {
    ALL: {
        value: 0,
        text: '全部',
    },
    REGISTRATION: {
        value: 1,
        text: '报名成功',
    },
    WAIT_START: {
        value: 2,
        text: '即将开赛',
    },
    STARTED: {
        value: 3,
        text: '进行中',
    },
    END: {
        value: 4,
        text: '已结束',
    },
};


/**
 * 优惠类型枚举
 */
export const PromotionDiscountTypeEnum = {
  PRICE: {
    type: 1,
    name: '满减',
  },
  PERCENT: {
    type: 2,
    name: '折扣',
  },
};

/**
 * 优惠劵模板的有限期类型的枚举
 */
export const CouponTemplateValidityTypeEnum = {
  DATE: {
    type: 1,
    name: '固定日期可用',
  },
  TERM: {
    type: 2,
    name: '领取之后可用',
  },
};

/**
 * 营销的商品范围枚举
 */
export const PromotionProductScopeEnum = {
  ALL: {
    scope: 1,
    name: '通用劵',
  },
  SPU: {
    scope: 2,
    name: '商品劵',
  },
  CATEGORY: {
    scope: 3,
    name: '品类劵',
  },
};


// 时间段的状态枚举
export const TimeStatusEnum = {
  WAIT_START: '即将开始',
  STARTED: '进行中',
  END: '已结束',
};

/**
 * 微信小程序的订阅模版
 */
export const WxaSubscribeTemplate = {
  TRADE_ORDER_DELIVERY: '订单发货通知',
  PROMOTION_COMBINATION_SUCCESS: '拼团结果通知',
  PAY_WALLET_RECHARGER_SUCCESS: '充值成功通知',
};
export const PromotionActivityTypeEnum = {
  NORMAL: {
    type: 0,
    name: '普通',
  },
  SECKILL: {
    type: 1,
    name: '秒杀',
  },
  BARGAIN: {
    type: 2,
    name: '砍价',
  },
  COMBINATION: {
    type: 3,
    name: '拼团',
  },
  POINT: {
    type: 4,
    name: '积分商城',
  },
};

export const getTimeStatusEnum = (startTime, endTime) => {
  const now = dayjs();
  if (now.isBefore(startTime)) {
    return TimeStatusEnum.WAIT_START;
  } else if (now.isAfter(endTime)) {
    return TimeStatusEnum.END;
  } else {
    return TimeStatusEnum.STARTED;
  }
};

// 球队颜色选项
export const TEAM_COLORS = [
  { value: 'red', text: '红色', color: '#FF0000' },
  { value: 'blue', text: '蓝色', color: '#0000FF' },
  { value: 'white', text: '白色', color: '#FFFFFF' },
  { value: 'black', text: '黑色', color: '#000000' },
  { value: 'yellow', text: '黄色', color: '#FFFF00' },
  { value: 'green', text: '绿色', color: '#008000' },
  { value: 'orange', text: '橙色', color: '#FFA500' },
  { value: 'purple', text: '紫色', color: '#800080' },
  { value: 'pink', text: '粉色', color: '#FFC0CB' },
  { value: 'navy', text: '深蓝色', color: '#000080' },
  { value: 'gray', text: '灰色', color: '#808080' },
  { value: 'burgundy', text: '酒红色', color: '#800020' },
];
