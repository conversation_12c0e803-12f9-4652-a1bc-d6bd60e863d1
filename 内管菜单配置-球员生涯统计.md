# 内管菜单配置 - 球员生涯统计功能

## 菜单配置概览

基于实际开发的球员生涯统计功能，以下是完整的菜单配置：

## 菜单层级结构

```
联盟管理 (ID: 2758)
└── 球员管理 (ID: 2766)
    ├── 球员生涯统计 (ID: 2900)
    │   ├── 生涯统计查询 (ID: 2901)
    │   ├── 生涯统计刷新 (ID: 2902)
    │   └── 生涯统计导出 (ID: 2903)
    ├── 生涯统计排行榜 (ID: 2904)
    │   ├── 排行榜查询 (ID: 2905)
    │   └── 排行榜导出 (ID: 2906)
    └── 球员对比 (ID: 2907)
        ├── 球员对比查询 (ID: 2908)
        └── 球员对比导出 (ID: 2909)
```

## 完整菜单配置SQL

### 1. 球员生涯统计主菜单

```sql
-- 球员生涯统计主菜单
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2900, '球员生涯统计', '', 2, 1, 2766, 'player-career-stats', 'ep:data-analysis', 
    'league/player/careerStats/index', 'PlayerCareerStats', 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);
```

**配置说明：**
- **菜单ID**: 2900
- **菜单名称**: 球员生涯统计
- **父菜单ID**: 2766 (球员管理)
- **路由路径**: player-career-stats
- **图标**: ep:data-analysis
- **组件路径**: league/player/careerStats/index
- **组件名称**: PlayerCareerStats

### 2. 球员生涯统计权限

```sql
-- 生涯统计查询权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2901, '生涯统计查询', 'league:player-career-stats:query', 3, 1, 2900, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 生涯统计刷新权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2902, '生涯统计刷新', 'league:player-career-stats:refresh', 3, 2, 2900, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 生涯统计导出权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2903, '生涯统计导出', 'league:player-career-stats:export', 3, 3, 2900, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);
```

### 3. 生涯统计排行榜菜单

```sql
-- 生涯统计排行榜主菜单
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2904, '生涯统计排行榜', '', 2, 2, 2766, 'career-stats-leaderboard', 'ep:histogram', 
    'league/player/careerStats/leaderboard', 'CareerStatsLeaderboard', 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 排行榜查询权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2905, '排行榜查询', 'league:player-career-stats:leaderboard:query', 3, 1, 2904, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 排行榜导出权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2906, '排行榜导出', 'league:player-career-stats:leaderboard:export', 3, 2, 2904, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);
```

### 4. 球员对比菜单

```sql
-- 球员对比主菜单
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2907, '球员对比', '', 2, 3, 2766, 'player-comparison', 'ep:connection', 
    'league/player/comparison/index', 'PlayerComparison', 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 球员对比查询权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2908, '球员对比查询', 'league:player-career-stats:comparison:query', 3, 1, 2907, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);

-- 球员对比导出权限
INSERT INTO `system_menu` (
    `id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, 
    `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, 
    `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    2909, '球员对比导出', 'league:player-career-stats:comparison:export', 3, 2, 2907, '', '', 
    NULL, NULL, 0, b'1', b'1', b'1', 
    '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'
);
```

## 前端组件配置

### 1. 球员生涯统计组件
- **路径**: `league/player/careerStats/index.vue`
- **功能**: 球员生涯统计查询、详情查看、数据刷新、导出

### 2. 生涯统计排行榜组件
- **路径**: `league/player/careerStats/leaderboard.vue`
- **功能**: 各项数据排行榜展示、筛选、导出

### 3. 球员对比组件
- **路径**: `league/player/comparison/index.vue`
- **功能**: 多球员数据对比、雷达图对比、导出对比结果

## 权限配置

### 角色权限分配

```sql
-- 给管理员角色添加权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`) 
SELECT 1, id FROM `system_menu` WHERE `permission` LIKE 'league:player-career-stats:%' OR `permission` LIKE 'league:player-career-stats:leaderboard:%' OR `permission` LIKE 'league:player-career-stats:comparison:%';

-- 给运营角色添加权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`) 
SELECT 101, id FROM `system_menu` WHERE `permission` LIKE 'league:player-career-stats:%' OR `permission` LIKE 'league:player-career-stats:leaderboard:%' OR `permission` LIKE 'league:player-career-stats:comparison:%';
```

## 功能特性

### 1. 球员生涯统计
- **数据展示**: 基础统计、高级统计、生涯总计、赛季统计
- **数据筛选**: 按球员、赛季、比赛类型筛选
- **实时刷新**: 支持手动刷新和自动刷新
- **导出功能**: Excel导出、PDF导出

### 2. 生涯统计排行榜
- **多维度排名**: 得分、篮板、助攻、效率值等
- **筛选功能**: 按赛季、比赛类型、位置筛选
- **排行榜类型**: 总榜、赛季榜、历史榜
- **数据导出**: 排行榜数据导出

### 3. 球员对比
- **多球员对比**: 支持2-5名球员同时对比
- **可视化对比**: 雷达图、柱状图对比
- **数据维度**: 基础数据、高级数据、生涯数据
- **对比结果导出**: 对比报告导出

## 执行SQL脚本

```sql
-- 完整执行脚本
-- 1. 球员生涯统计菜单
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (2900, '球员生涯统计', '', 2, 1, 2766, 'player-career-stats', 'ep:data-analysis', 'league/player/careerStats/index', 'PlayerCareerStats', 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 2. 生涯统计权限
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES 
(2901, '生涯统计查询', 'league:player-career-stats:query', 3, 1, 2900, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'),
(2902, '生涯统计刷新', 'league:player-career-stats:refresh', 3, 2, 2900, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'),
(2903, '生涯统计导出', 'league:player-career-stats:export', 3, 3, 2900, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 3. 生涯统计排行榜菜单
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (2904, '生涯统计排行榜', '', 2, 2, 2766, 'career-stats-leaderboard', 'ep:histogram', 'league/player/careerStats/leaderboard', 'CareerStatsLeaderboard', 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 4. 排行榜权限
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES 
(2905, '排行榜查询', 'league:player-career-stats:leaderboard:query', 3, 1, 2904, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'),
(2906, '排行榜导出', 'league:player-career-stats:leaderboard:export', 3, 2, 2904, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 5. 球员对比菜单
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (2907, '球员对比', '', 2, 3, 2766, 'player-comparison', 'ep:connection', 'league/player/comparison/index', 'PlayerComparison', 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 6. 球员对比权限
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES 
(2908, '球员对比查询', 'league:player-career-stats:comparison:query', 3, 1, 2907, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0'),
(2909, '球员对比导出', 'league:player-career-stats:comparison:export', 3, 2, 2907, '', '', NULL, NULL, 0, b'1', b'1', b'1', '1', '2024-08-02 10:00:00', '1', '2024-08-02 10:00:00', b'0');

-- 7. 角色权限分配
INSERT INTO `system_role_menu` (`role_id`, `menu_id`) SELECT 1, id FROM `system_menu` WHERE `permission` LIKE 'league:player-career-stats:%' OR `permission` LIKE 'league:player-career-stats:leaderboard:%' OR `permission` LIKE 'league:player-career-stats:comparison:%';
INSERT INTO `system_role_menu` (`role_id`, `menu_id`) SELECT 101, id FROM `system_menu` WHERE `permission` LIKE 'league:player-career-stats:%' OR `permission` LIKE 'league:player-career-stats:leaderboard:%' OR `permission` LIKE 'league:player-career-stats:comparison:%';
```

## 验证清单

1. **菜单显示**: 检查球员管理下是否正确显示三个子菜单
2. **权限控制**: 验证不同角色的权限是否正确
3. **功能测试**: 测试所有功能是否正常工作
4. **组件路径**: 确认前端组件路径正确
5. **数据准确性**: 验证统计数据的准确性

## 注意事项

1. **菜单ID**: 2900-2909 是预留的，请根据实际情况调整
2. **父菜单ID**: 2766 是球员管理菜单的ID，请根据实际情况调整
3. **组件路径**: 确保前端组件文件存在且路径正确
4. **权限分配**: 根据实际角色需求调整权限分配
5. **图标选择**: 可根据需要调整菜单图标