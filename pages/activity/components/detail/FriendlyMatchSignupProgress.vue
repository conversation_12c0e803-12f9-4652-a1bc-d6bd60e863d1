<template>
  <view class="signup-progress team-progress">
    <!-- 标题栏: 显示已报名球队数量 -->
    <view class="header" @click="toggleExpand">
      <view class="header-left">
        <uni-icons type="flag-filled" size="20" color="#4a90e2"></uni-icons> <!-- 使用不同图标 -->
        <view class="title">
          <text>已报名球队 </text>
          <text class="count-parenthesis">({{ currentTeams }}/{{ maxTeams }})</text>
        </view>
      </view>
      <view class="header-right">
        <view class="toggle-btn">
          <text>{{ isExpanded ? '收起' : '展开' }}</text>
          <uni-icons 
            type="arrowdown" 
            size="16" 
            color="#999"
            :class="{'arrow-expanded': isExpanded}"
          ></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 球队Logo列表区域 - 通过CSS控制显隐 -->
    <view 
      class="team-logo-list-container logo-collapsible-content" 
      :class="{ 'expanded': !isExpanded }" 
    >
      <scroll-view scroll-x="true" class="team-logo-scroll">
        <view class="team-logo-wrapper" v-for="team in signedUpTeams" :key="'team-logo-'+team.id">
          <image class="team-logo" :src="team.logoUrl || '/static/default-team-logo.png'" mode="aspectFill"></image>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" v-if="!signedUpTeams || signedUpTeams.length === 0">
          <text>暂无球队报名</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 展开后的队伍详情区域 -->
    <view 
      class="team-details-content collapsible-content" 
      :class="{ expanded: isExpanded }"
    >
      <view class="team-block" v-for="team in signedUpTeams" :key="'team-detail-'+team.id">
        <!-- 队伍标题栏 - 点击跳转 -->
        <view 
          class="team-detail-header"
          :style="{ backgroundColor: hexToRgba(team.color || defaultTeamColor(team.id), 0.85) }"
          @click="navigateToTeamInfo(team.id)" 
        >
          <image class="header-team-logo" :src="team.logoUrl || '/static/default-team-logo.png'" mode="aspectFill"></image>
          <view class="header-team-info">
            {{ team.name || '球队名称' }} ({{ team.players?.length || 0 }}/{{ team.maxPlayers || 10 }})
          </view>
        </view>
        <!-- 队伍成员头像列表 -->
        <scroll-view scroll-x="true" class="team-player-scroll">
          <view class="team-player-wrapper" v-for="player in team.players" :key="'team-'+team.id+'-player-'+player.id" @click="navigateToPlayerCareer(player)">
            <image class="team-player-avatar" :src="player.avatar || '/static/default-avatar.jpg'" mode="aspectFill"></image>
            <text class="team-player-name">{{ player.nickname || '球员' }}</text>
          </view>
          <view class="empty-team-players" v-if="!team.players || team.players.length === 0">
            <text>该队暂无球员</text>
          </view>
        </scroll-view>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import $router from '@/sheep/router';

// 定义组件属性
const props = defineProps({
  currentTeams: {
    type: Number,
    default: 0
  },
  maxTeams: {
    type: Number,
    default: 2 // 友谊赛通常是2队
  },
  signedUpTeams: {
    type: Array,
    default: () => [] // 格式: [{ id, name, logoUrl, players: [{ id, avatar, nickname }], color?, maxPlayers? }, ...]
  },
});

// 组件内部状态
const isExpanded = ref(false);

// 默认队伍颜色列表，用于没有指定颜色时循环使用
const defaultColors = ['#ff4d4f', '#34c759', '#4a90e2', '#f5a623', '#9013fe'];

// 方法
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 根据队伍ID获取一个默认颜色
const defaultTeamColor = (teamId) => {
  const index = teamId % defaultColors.length;
  return defaultColors[index];
};

// 导航到球员生涯页面
const navigateToPlayerCareer = (player) => {
  if (!player || !player.id) { 
    uni.showToast({
      title: '无法查看球员详情',
      icon: 'none',
    });
    return;
  }
  const url = `/pages/player/player-career-other?playerId=${player.id}`;
  $router.go(url);
};

// 导航到球队信息页面
const navigateToTeamInfo = (teamId) => {
  if (!teamId) {
    uni.showToast({
      title: '无法查看球队详情',
      icon: 'none',
    });
    return;
  }
  const url = `/pages/team/team-info?id=${teamId}`;
  $router.go(url);
};

// Helper: 将 HEX 颜色转换为 RGBA
function hexToRgba(hex, alpha = 1) {
  // 移除 # 号
  hex = hex.replace('#', '');

  // 处理缩写形式 (e.g., #03F)
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

</script>

<style lang="scss" scoped>
// 基础颜色变量 (可从全局导入)
$primary-color: #4a90e2;
$danger-color: #ff4d4f;
$success-color: #34c759;
$border-color: #f0f0f0;
$bg-color: #ffffff;
$text-color: #333333;
$text-secondary: #999999;

// 主容器
.signup-progress.team-progress {
  background-color: $bg-color;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

// 标题栏 (与排位赛类似，可考虑提取公共样式)
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  cursor: pointer;
  border-bottom: 1px solid $border-color;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12rpx;
    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: $text-color;
      .count-parenthesis {
        color: $text-color;
        margin-left: 4rpx;
      }
    }
  }

  .header-right {
    .toggle-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background-color: #f5f7fa;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #999;
      gap: 4rpx;
      .arrow-expanded {
        transform: rotate(180deg);
        transition: transform 0.3s;
      }
    }
  }
}

// 球队Logo列表容器 - 添加动画
.team-logo-list-container {
  // 与 collapsible-content 类似的动画属性
  max-height: 120rpx; // 估算一个合适的最大高度 (80rpx logo + padding)
  opacity: 1;
  overflow: hidden;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out, padding 0.4s ease-out;
  padding: 20rpx 30rpx; // 默认展开状态的 padding
  box-sizing: border-box;

  // 添加 .logo-collapsible-content 类以应用通用动画
  &.logo-collapsible-content {
    // 初始（隐藏）状态
    &:not(.expanded) {
      max-height: 0;
      opacity: 0;
      padding-top: 0;
      padding-bottom: 0;
      // 可能需要设置 border-bottom 为 none 或透明
      border-bottom-color: transparent;
      transition: max-height 0.4s ease-out, opacity 0.3s ease-out, padding 0.4s ease-out, border-color 0.4s ease-out; 
    }
    // 展开状态（由 :class="{ 'expanded': !isExpanded }" 控制）
    &.expanded {
      max-height: 120rpx; // 恢复高度
      opacity: 1;
      padding-top: 20rpx;
      padding-bottom: 20rpx;
      border-bottom-color: $border-color; // 恢复边框颜色
    }
  }
}

// Logo滚动区域
.team-logo-scroll {
  white-space: nowrap;
  &::-webkit-scrollbar {
    display: none;
  }

  .team-logo-wrapper {
    display: inline-block;
    margin-right: 20rpx;
    &:last-child {
      margin-right: 0;
    }
  }
  
  .team-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    border: 1px solid $border-color;
  }

  .empty-state {
    padding: 20rpx 0;
    text-align: left;
    color: $text-secondary;
    font-size: 26rpx;
  }
}

// 展开后的队伍详情区域
.team-details-content {
  padding: 0 30rpx 24rpx;
}

// 可折叠内容容器样式
.collapsible-content {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out, padding 0.4s ease-out; // 添加 padding 过渡
  padding-top: 0; // 初始状态无上边距
  padding-bottom: 0; // 初始状态无下边距
  box-sizing: border-box;
}

.collapsible-content.expanded {
  max-height: 1000px; // 设置一个足够大的最大高度
  opacity: 1;
  padding-top: 20rpx; // 展开后的上边距
  padding-bottom: 24rpx; // 展开后的下边距（与原 padding 一致）
}

.team-block {
  margin-bottom: 20rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

// 队伍标题栏
.team-detail-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.header-team-logo {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.header-team-info {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 队伍成员头像滚动区域
.team-player-scroll {
  white-space: nowrap;
  &::-webkit-scrollbar {
    display: none;
  }

  .team-player-wrapper {
    display: inline-block;
    text-align: center;
    margin-right: 24rpx;
    vertical-align: top;
    width: 110rpx; // 固定宽度以对齐
    &:last-child {
      margin-right: 0;
    }
  }
  
  .team-player-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin-bottom: 8rpx;
  }
  
  .team-player-name {
    display: block;
    font-size: 24rpx;
    color: $text-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
  
  .empty-team-players {
    padding: 30rpx 0;
    text-align: left;
    color: $text-secondary;
    font-size: 26rpx;
  }
}

</style> 