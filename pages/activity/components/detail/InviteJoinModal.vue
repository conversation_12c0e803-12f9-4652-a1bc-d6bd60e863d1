<template>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="invite-modal">
      <view class="modal-header">
        <text class="title">好友邀请</text>
        <view class="close-btn" @click="handleCancel">
          <uni-icons type="close" size="20" color="#999"></uni-icons>
        </view>
      </view>
      
      <view class="modal-content">
        <view class="invite-info">
          <view class="leader-info">
            <image 
              class="avatar" 
              :src="inviteInfo?.leaderAvatar || '/static/images/default-avatar.png'"
              mode="aspectFill"
            />
            <view class="leader-details">
              <text class="leader-name">{{ inviteInfo?.leaderName || '好友' }}</text>
              <text class="invite-text">邀请你加入好友组队</text>
            </view>
          </view>
          
          <view class="room-info">
            <view class="info-row">
              <text class="label">活动名称</text>
              <text class="value">{{ inviteInfo?.activityName }}</text>
            </view>
            <view class="info-row">
              <text class="label">已加入成员</text>
              <text class="value">{{ inviteInfo?.currentMembers || 0 }}人</text>
            </view>
            <view class="info-row">
              <text class="label">剩余名额</text>
              <text class="value">{{ remainingSlots }}人</text>
            </view>
            <view class="info-row" v-if="inviteInfo?.expiresAt">
              <text class="label">邀请有效期</text>
              <text class="value expire-time">{{ formatExpireTime(inviteInfo.expiresAt) }}</text>
            </view>
          </view>
          
          <view class="members-list" v-if="inviteInfo?.members && inviteInfo.members.length > 0">
            <text class="members-title">组队成员</text>
            <view class="members-grid">
              <view 
                class="member-item" 
                v-for="member in inviteInfo.members" 
                :key="member.userId"
              >
                <image 
                  class="member-avatar" 
                  :src="member.avatar || '/static/images/default-avatar.png'"
                  mode="aspectFill"
                />
                <text class="member-name">{{ member.name }}</text>
                <view class="leader-badge" v-if="member.isLeader">队长</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="confirm-btn" @click="handleConfirm" :disabled="isExpired">
          {{ isExpired ? '邀请已过期' : '立即加入' }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  inviteInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const popup = ref(null);

// 计算属性
const remainingSlots = computed(() => {
  const maxMembers = props.inviteInfo?.maxMembers || 3;
  const currentMembers = props.inviteInfo?.currentMembers || 0;
  return Math.max(0, maxMembers - currentMembers);
});

const isExpired = computed(() => {
  if (!props.inviteInfo?.expiresAt) return false;
  return dayjs().isAfter(dayjs(props.inviteInfo.expiresAt));
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    popup.value?.open();
  } else {
    popup.value?.close();
  }
}, { immediate: true });

// 事件处理
const handleConfirm = () => {
  if (isExpired.value) {
    uni.showToast({
      title: '邀请已过期',
      icon: 'none'
    });
    return;
  }
  
  if (remainingSlots.value <= 0) {
    uni.showToast({
      title: '房间已满',
      icon: 'none'
    });
    return;
  }
  
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
};

// 工具方法
const formatExpireTime = (expireTime) => {
  const now = dayjs();
  const expire = dayjs(expireTime);
  
  if (expire.isBefore(now)) {
    return '已过期';
  }
  
  const diffMinutes = expire.diff(now, 'minute');
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟后过期`;
  }
  
  const diffHours = expire.diff(now, 'hour');
  if (diffHours < 24) {
    return `${diffHours}小时后过期`;
  }
  
  return expire.format('MM月DD日 HH:mm过期');
};

onMounted(() => {
  if (props.visible) {
    popup.value?.open();
  }
});
</script>

<style lang="scss" scoped>
.invite-modal {
  width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  
  .modal-header {
    position: relative;
    padding: 40rpx 30rpx 20rpx;
    text-align: center;
    border-bottom: 1px solid #f5f5f5;
    
    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .modal-content {
    padding: 30rpx;
    
    .invite-info {
      .leader-info {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        
        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }
        
        .leader-details {
          flex: 1;
          
          .leader-name {
            display: block;
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
          }
          
          .invite-text {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
      
      .room-info {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 30rpx;
        
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12rpx 0;
          
          &:not(:last-child) {
            border-bottom: 1px solid #eee;
          }
          
          .label {
            font-size: 28rpx;
            color: #666;
          }
          
          .value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            
            &.expire-time {
              color: #ff6b6b;
            }
          }
        }
      }
      
      .members-list {
        .members-title {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 20rpx;
          font-weight: 500;
        }
        
        .members-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 20rpx;
          
          .member-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 120rpx;
            
            .member-avatar {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              margin-bottom: 8rpx;
            }
            
            .member-name {
              font-size: 24rpx;
              color: #666;
              text-align: center;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .leader-badge {
              position: absolute;
              top: -8rpx;
              right: 10rpx;
              background: #ff6b6b;
              color: #fff;
              font-size: 20rpx;
              padding: 4rpx 8rpx;
              border-radius: 8rpx;
              transform: scale(0.8);
            }
          }
        }
      }
    }
  }
  
  .modal-actions {
    display: flex;
    border-top: 1px solid #f5f5f5;
    
    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 30rpx;
      margin: 0;
      border: none;
      border-radius: 0;
    }
    
    .cancel-btn {
      background: #f8f9fa;
      color: #666;
      border-right: 1px solid #f5f5f5;
    }
    
    .confirm-btn {
      background: #4080FF;
      color: #ffffff;
      
      &:disabled {
        background: #cccccc;
        color: #999;
      }
    }
  }
}
</style> 