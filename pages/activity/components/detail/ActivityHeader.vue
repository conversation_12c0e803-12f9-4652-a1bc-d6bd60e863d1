<template>
  <view class="activity-header">
    <!-- 背景图 -->
    <image
      class="header-background"
      :src="activity.basicInfo?.coverUrl"
      mode="aspectFill"
    />
    
    <!-- 活动类型标签区域 -->
    <view class="activity-type-tags">
      <view class="type-tag primary-tag">
        <uni-icons type="calendar" size="16" color="#fff"></uni-icons>
        <text>{{ getTypeText(activity.type) }}</text>
      </view>
      <view class="type-tag secondary-tag">
        <uni-icons type="staff" size="16" color="#fff"></uni-icons>
        <text>{{ getDailyActivityType(activity.gameConfig?.gameType) }}</text>
      </view>
    </view>
    
    <!-- 标题和场地信息 -->
    <view class="header-content">
      <view class="activity-title">{{ activity.basicInfo?.title }}</view>
      <!-- <view class="activity-venue">{{ activity.basicInfo?.venue || activity.basicInfo?.location }}</view> -->
      <view class="activity-address">
        <uni-icons type="location" size="16" color="rgba(255,255,255,0.8)"></uni-icons>
        <text>{{ activity.basicInfo?.location || '' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { ACTIVITY_TYPE } from '@/sheep/store/activity';
import { getDailyActivityType } from '@/sheep/util/daily';

const props = defineProps({
  activity: {
    type: Object,
    required: true,
    default: () => ({
      basicInfo: {},
      venueInfo: {},
      gameConfig: {},
    })
  }
});

const emit = defineEmits(['share']);

// 获取活动类型文本
const getTypeText = (type) => {
  const typeMap = {
    [ACTIVITY_TYPE.RANKING_MATCH]: '排位赛',
    [ACTIVITY_TYPE.FRIENDLY_MATCH]: '友谊赛',
    [ACTIVITY_TYPE.LEAGUE]: '联赛'
  };
  return typeMap[type] || '比赛';
};

// 分享活动（保留方法供父组件调用）
const shareActivity = () => {
  emit('share');
};
</script>

<style lang="scss" scoped>
.activity-header {
  position: relative;
  height: 460rpx;
  overflow: hidden;
  
  .header-background {
    position: absolute;
    width: 100%;
    height: 100%;
    filter: brightness(0.7);
  }
  
  .activity-type-tags {
    position: absolute;
    z-index: 2;
    display: flex;
    flex-direction: row;
    gap: 12rpx;
    padding: 0;
    top: calc(75rpx + env(safe-area-inset-top));
    left: 30rpx;
    
    .type-tag {
      background-color: rgba(0, 0, 0, 0.4);
      border-radius: 30rpx;
      padding: 8rpx 20rpx;
      display: flex;
      align-items: center;
      align-self: flex-start;
      
      .uni-icons {
        margin-right: 6rpx;
      }
      
      text {
        color: white;
        font-size: 24rpx;
      }
    }
    
    .primary-tag {
      background-color: rgba(227, 62, 51, 0.7);
    }
    
    .secondary-tag {
      background-color: rgba(89, 87, 87, 0.7);
    }
  }
  
  .header-content {
    position: absolute;
    z-index: 2;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    bottom: 40rpx;
    left: 0;
    right: 0;
    
    .activity-title {
      color: #ffffff;
      font-size: 56rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .activity-venue {
      color: #ffffff;
      font-size: 36rpx;
      margin-bottom: 8rpx;
    }
    
    .activity-address {
      display: flex;
      align-items: center;
      
      .uni-icons {
        margin-right: 6rpx;
      }
      
      text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 22rpx;
      }
    }
  }
}
</style> 