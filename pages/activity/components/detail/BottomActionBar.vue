<template>
  <view class="bottom-action-bar">
    <view class="price-info">
      <text class="price-label">{{ priceLabel }}</text>
      <text class="price-value">¥{{ formatPrice(estimatedFee) }}</text>
      <text class="price-unit">{{ priceUnit }}</text>
    </view>
    <view class="action-buttons">
      <button class="share-button" open-type="share" @click="handleShare">
        <uni-icons type="redo" size="18" color="#4080FF"></uni-icons>
        <text>分享</text>
      </button>
      <button class="signup-button" :class="signupButtonClass" @click="handleSignup">
        {{ signupButtonText }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { ACTIVITY_STATUS, ACTIVITY_TYPE, SIGNUP_MODE, USER_SIGNUP_STATUS } from '@/sheep/store/activity';

const props = defineProps({
  activity: {
    type: Object,
    required: true
  },
  signupMode: {
    type: Number,
    default: 1
  },
  isActivityFull: {
    type: Boolean,
    default: false
  },
  estimatedFee: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['share', 'signup', 'withdraw', 'pay']);

// 计算属性
const priceLabel = computed(() => {
  if (!props.activity) return '';
  
  switch (props.activity.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      return '预估费用';
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      return '球队费用';
    case ACTIVITY_TYPE.LEAGUE:
      return '报名费用';
    default:
      return '费用';
  }
});

const priceUnit = computed(() => {
  if (!props.activity) return '';
  
  switch (props.activity.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      return '/人';
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      return '/队';
    case ACTIVITY_TYPE.LEAGUE:
      return '/人';
    default:
      return '';
  }
});

const signupButtonText = computed(() => {
  if (!props.activity) return '';
  
  // 检查用户当前报名状态 - 使用常量替换魔法值
  const currentStatus = props.activity.signupRelatedInfo?.currentUserSignupStatus;
  
  // 🔥新增：如果已报名但未支付，显示立即支付
  if (currentStatus === USER_SIGNUP_STATUS.PENDING_PAYMENT) {
    return '立即支付';
  }
  
  // 如果已报名并支付成功，显示申请退赛
  if (currentStatus === USER_SIGNUP_STATUS.PAID || 
      currentStatus === USER_SIGNUP_STATUS.REGISTRATION_SUCCESS || 
      currentStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED) {
    return '申请退赛';
  }
  
  // 根据活动状态和人数情况决定按钮文字
  if (props.activity.status === ACTIVITY_STATUS.ENROLLING) {
    const buttonText = props.isActivityFull ? '加入候补' : '立即报名';
    return buttonText;
  }
  if (props.activity.status === ACTIVITY_STATUS.ENROLLMENT_ENDED) {
    return '加入候补';
  }
  // 🔥新增：处理组局成功状态 - 在此状态下仍可报名或候补
  if (props.activity.status === ACTIVITY_STATUS.GROUPING_SUCCESSFUL) {
    const buttonText = props.isActivityFull ? '加入候补' : '立即报名';
    return buttonText;
  }
  
  return '立即报名';
});

const signupButtonClass = computed(() => {
  if (!props.activity) return {};
  
  const currentStatus = props.activity.signupRelatedInfo?.currentUserSignupStatus;
  
  // 🔥新增：待支付状态的样式 - 醒目的支付样式
  if (currentStatus === USER_SIGNUP_STATUS.PENDING_PAYMENT) {
    return { 'pending-payment': true };
  }
  
  // 已报名状态的样式 - 改为退赛样式
  if (currentStatus === USER_SIGNUP_STATUS.PAID || 
      currentStatus === USER_SIGNUP_STATUS.REGISTRATION_SUCCESS || 
      currentStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED) {
    return { 'withdraw': true };
  }
  
  // 候补状态的样式
  if ((props.activity.status === ACTIVITY_STATUS.ENROLLING && props.isActivityFull) ||
      props.activity.status === ACTIVITY_STATUS.ENROLLMENT_ENDED ||
      (props.activity.status === ACTIVITY_STATUS.GROUPING_SUCCESSFUL && props.isActivityFull)) {
    return { 'waitlist': true };
  }
  
  return {};
});

// 事件处理
const handleShare = () => {
  emit('share');
};

const handleSignup = () => {
  const currentStatus = props.activity.signupRelatedInfo?.currentUserSignupStatus;
  
  // 🔥新增：如果是待支付状态，触发支付流程
  if (currentStatus === USER_SIGNUP_STATUS.PENDING_PAYMENT) {
    emit('pay'); // 新增支付事件
    return;
  }
  
  // 如果已报名，触发退赛流程
  if (currentStatus === USER_SIGNUP_STATUS.PAID || 
      currentStatus === USER_SIGNUP_STATUS.REGISTRATION_SUCCESS || 
      currentStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED) {
    emit('withdraw');
    return;
  }
  
  emit('signup');
};

// 工具方法
const formatPrice = (price) => {
  return (price / 100).toFixed(2);
};
</script>

<style lang="scss" scoped>
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
  z-index: 99;
  
  .price-info {
    flex: 1;
    display: flex;
    align-items: baseline;
    
    .price-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 10rpx;
    }
    
    .price-value {
      font-size: 36rpx;
      color: #FF4D4F;
      font-weight: bold;
    }
    
    .price-unit {
      font-size: 24rpx;
      color: #999;
      margin-left: 4rpx;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 15rpx;
    
    .share-button {
      height: 70rpx;
      line-height: 70rpx;
      padding: 0 30rpx;
      background-color: #ffffff;
      border: 1px solid #4080FF;
      color: #ffffff;
      font-size: 28rpx;
      border-radius: 35rpx;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      text {
        color: #4080FF;
        margin-left: 6rpx;
      }
    }
    
    .signup-button {
      height: 70rpx;
      line-height: 70rpx;
      padding: 0 40rpx;
      background-color: #4080FF;
      color: #ffffff;
      font-size: 28rpx;
      border-radius: 35rpx;
      margin: 0;
      box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
      transition: all 0.3s ease;
      
      &.disabled {
        background-color: #cccccc;
        box-shadow: none;
      }
      
      &.waitlist {
        background-color: #FF9800;
        box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
      }
      
      &.withdraw {
        background-color: #f5f5f5;
        color: #999999;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
        
        &:hover {
          background-color: #eeeeee;
          border-color: #d0d0d0;
        }
        
        &:active {
          background-color: #e8e8e8;
          border-color: #c0c0c0;
        }
      }
      
      &.pending-payment {
        background-color: #FF9800;
        box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
      }
    }
  }
}
</style> 