<template>
  <view class="signup-deadline-card" :class="getCardClass()">
    <view class="deadline-header">
      <uni-icons type="clock" size="20" :color="getIconColor()"></uni-icons>
      <view class="countdown-container">
        <template v-if="isExpired">
          <text class="expired-text" :class="getExpiredStatusClass()">{{ getExpiredStatusText() }}</text>
        </template>
        <template v-else>
          <view class="countdown-parts">
            <template v-for="(part, index) in countdownParts" :key="index">
              <text class="countdown-number">{{ part.value }}</text>
              <text class="countdown-unit">{{ part.unit }}</text>
              <text v-if="part.separator" class="countdown-separator">{{ part.separator }}</text>
            </template>
            <text class="countdown-suffix">后停止组局</text>
          </view>
        </template>
      </view>
    </view>
    <view class="deadline-note" v-if="note">
      <text>注：{{ note }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ACTIVITY_STATUS } from '@/sheep/store/activity';
import dayjs from 'dayjs';

const props = defineProps({
  deadline: {
    type: [String, Number, Date],
    required: true
  },
  status: {
    type: Number,
    default: ACTIVITY_STATUS.ENROLLING // 默认为报名中
  },
  note: {
    type: String,
    default: '报名人数满16人组局成功。组局失败，活动自动取消，费用将原路退回'
  },
  minPlayersToStart: {
    type: Number,
    default: 16
  }
});

// 倒计时相关状态
const now = ref(dayjs());
const timer = ref(null);

// 计算属性
const deadlineTime = computed(() => {
  return dayjs(props.deadline);
});

const duration = computed(() => {
  if (deadlineTime.value.isBefore(now.value)) return null;
  return dayjs.duration(deadlineTime.value.diff(now.value));
});

// 是否已过期
const isExpired = computed(() => {
  return deadlineTime.value.isBefore(now.value);
});

// 是否紧急（小于24小时）
const isUrgent = computed(() => {
  if (isExpired.value) return false;
  const diff = deadlineTime.value.diff(now.value, 'hour');
  return diff <= 24;
});

// 倒计时结构化数据 - 用于分段显示不同样式
const countdownParts = computed(() => {
  if (isExpired.value || !duration.value) return [];
  
  const days = Math.floor(duration.value.asDays());
  const hours = duration.value.hours().toString().padStart(2, '0');
  const minutes = duration.value.minutes().toString().padStart(2, '0');
  const seconds = duration.value.seconds().toString().padStart(2, '0');
  
  const parts = [];
  
  if (days > 0) {
    parts.push({ value: days, unit: '天' });
  }
  
  parts.push(
    { value: hours, unit: '', separator: ':' },
    { value: minutes, unit: '', separator: ':' },
    { value: seconds, unit: '' }
  );
  
  return parts;
});

// 获取过期状态文本
const getExpiredStatusText = () => {
  // 检查活动状态，如果是组局成功相关状态，显示对应文本
  const status = props.status;
  
  // ACTIVITY_STATUS 枚举值参考：
  // GROUPING_SUCCESSFUL = 8, IN_PROGRESS = 4, COMPLETED = 5
  if (status === 8) {
    return '组局成功';
  } else if (status === 4) {
    return '比赛进行中';
  } else if (status === 5) {
    return '比赛已结束';
  } else {
    return '报名已截止';
  }
};

// 获取过期状态对应的CSS类
const getExpiredStatusClass = () => {
  const status = props.status;
  
  if (status === 8) {
    return 'status-success'; // 组局成功 - 绿色
  } else if (status === 4) {
    return 'status-in-progress'; // 比赛进行中 - 蓝色  
  } else if (status === 5) {
    return 'status-completed'; // 比赛已结束 - 紫色
  } else {
    return 'status-expired'; // 默认过期 - 灰色
  }
};

// 获取图标颜色
const getIconColor = () => {
  if (isExpired.value) {
    const status = props.status;
    
    if (status === 8) {
      return '#4CAF50'; // 组局成功 - 绿色
    } else if (status === 4) {
      return '#2196F3'; // 比赛进行中 - 蓝色
    } else if (status === 5) {
      return '#9C27B0'; // 比赛已结束 - 紫色
    } else {
      return '#999'; // 默认过期 - 灰色
    }
  } else {
    return '#FF9800'; // 倒计时进行中 - 橙色
  }
};

// 获取卡片CSS类
const getCardClass = () => {
  const classes = [];
  
  if (isUrgent.value) {
    classes.push('status-urgent');
  }
  
  if (isExpired.value) {
    const status = props.status;
    
    if (status === 8) {
      classes.push('status-success'); // 组局成功
    } else if (status === 4) {
      classes.push('status-in-progress'); // 比赛进行中
    } else if (status === 5) {
      classes.push('status-completed'); // 比赛已结束
    } else {
      classes.push('status-expired'); // 默认过期
    }
  }
  
  return classes;
};

// 定时器
onMounted(() => {
  // 初始化时立即更新一次时间
  now.value = dayjs();
  
  timer.value = setInterval(() => {
    now.value = dayjs();
  }, 1000);
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>

<style lang="scss" scoped>
.signup-deadline-card {
  display: flex;
  flex-direction: column;
  padding: 20rpx 24rpx; /* 优化：从24rpx 30rpx减少到20rpx 24rpx，减少间距 */
  background-color: #FFF8E6;
  border-radius: 12rpx;
  margin: 0 0 16rpx 0; /* 优化：从20rpx减少到16rpx */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .deadline-header {
    display: flex;
    align-items: center;
    
    .uni-icons {
      margin-right: 15rpx;
    }
    
    .countdown-container {
      flex: 1;
      
      .expired-text {
        font-size: 32rpx;
        color: #999;
        font-weight: 500;
        
        &.status-success {
          color: #4CAF50; // 组局成功 - 绿色
        }
        
        &.status-in-progress {
          color: #2196F3; // 比赛进行中 - 蓝色
        }
        
        &.status-completed {
          color: #9C27B0; // 比赛已结束 - 紫色
        }
        
        &.status-expired {
          color: #999; // 默认过期 - 灰色
        }
      }
      
      .countdown-parts {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;
        
        .countdown-number {
          font-size: 36rpx; /* 数字更大更醒目 */
          color: #FF4D4F; /* 使用红色突出重要数字 */
          font-weight: 600;
          line-height: 1;
        }
        
        .countdown-unit {
          font-size: 28rpx;
          color: #FF9800; /* 单位保持橙色 */
          font-weight: 500;
          margin-right: 8rpx;
        }
        
        .countdown-separator {
          font-size: 32rpx;
          color: #FF4D4F;; /* 分隔符与数字同色 */
          font-weight: 600;
          margin: 0 2rpx;
        }
        
        .countdown-suffix {
          font-size: 28rpx;
          color: #FF9800;
          font-weight: 500;
          margin-left: 8rpx;
        }
      }
    }
  }
  
  .deadline-note {
    margin-top: 8rpx; /* 优化：从10rpx减少到8rpx */
    padding-left: 35rpx;
    
    text {
      font-size: 24rpx;
      color: #999;
      line-height: 1.4;
    }
  }
  
  &.status-urgent {
    background-color: #FFF0D9;
    
    .countdown-number {
      color: #D32F2F !important; /* 紧急状态数字用更深的红色 */
    }
    
    .countdown-unit,
    .countdown-suffix {
      color: #F57C00 !important;
    }
  }
  
  &.status-expired {
    background-color: #EEEEEE;
    
    .expired-text {
      color: #999 !important;
    }
    
    .deadline-note text {
      color: #999;
    }
  }
  
  // 组局成功状态
  &.status-success {
    background-color: #E8F5E8; // 淡绿色背景
    
    .expired-text {
      color: #4CAF50 !important;
    }
    
    .deadline-note text {
      color: #4CAF50;
    }
  }
  
  // 比赛进行中状态  
  &.status-in-progress {
    background-color: #E3F2FD; // 淡蓝色背景
    
    .expired-text {
      color: #2196F3 !important;
    }
    
    .deadline-note text {
      color: #2196F3;
    }
  }
  
  // 比赛已结束状态
  &.status-completed {
    background-color: #F3E5F5; // 淡紫色背景
    
    .expired-text {
      color: #9C27B0 !important;
    }
    
    .deadline-note text {
      color: #9C27B0;
    }
  }
}
</style> 