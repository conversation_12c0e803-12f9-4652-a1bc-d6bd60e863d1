<template>
  <InfoSection title="赛制说明" icon="compose">
    <view class="format-section">
      <view class="format-overview">
        <view class="format-title">
          <uni-icons type="flag" size="18" color="#4080FF"></uni-icons>
          <text>{{ getFormatTitle() }}</text>
        </view>
        <text class="format-description">{{ getFormatDescription() }}</text>
      </view>
      
      <view class="format-details" v-if="hasFormatDetails">
        <view class="detail-item" v-for="detail in getFormatDetails()" :key="detail.title">
          <view class="detail-header">
            <uni-icons :type="detail.icon" size="16" color="#666"></uni-icons>
            <text class="detail-title">{{ detail.title }}</text>
          </view>
          <text class="detail-content">{{ detail.content }}</text>
        </view>
      </view>
      
      <view class="scoring-rules" v-if="config?.scoringRules">
        <text class="rules-title">积分规则</text>
        <view class="rules-list">
          <view class="rule-item">
            <text class="rule-label">胜利</text>
            <text class="rule-value">{{ config.scoringRules.win || 3 }}分</text>
          </view>
          <view class="rule-item" v-if="config.scoringRules.draw">
            <text class="rule-label">平局</text>
            <text class="rule-value">{{ config.scoringRules.draw }}分</text>
          </view>
          <view class="rule-item">
            <text class="rule-label">失败</text>
            <text class="rule-value">{{ config.scoringRules.loss || 0 }}分</text>
          </view>
        </view>
      </view>
      
      <view class="timeline" v-if="hasTimeline">
        <text class="timeline-title">赛程安排</text>
        <view class="timeline-list">
          <view class="timeline-item" v-for="(phase, index) in getTimelinePhases()" :key="index">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <text class="phase-name">{{ phase.name }}</text>
              <text class="phase-time">{{ phase.time }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import { computed } from 'vue';
import InfoSection from './InfoSection.vue';

const props = defineProps({
  formatType: {
    type: Number,
    default: 1
  },
  config: {
    type: Object,
    default: () => ({})
  }
});

// 计算属性
const hasFormatDetails = computed(() => {
  return props.formatType && props.formatType > 0;
});

const hasTimeline = computed(() => {
  return props.config?.timeline || props.formatType;
});

// 方法
const getFormatTitle = () => {
  const formatTitles = {
    1: '循环赛制',
    2: '单败淘汰赛',
    3: '小组循环+淘汰赛',
    4: '双败淘汰赛'
  };
  
  return formatTitles[props.formatType] || '自定义赛制';
};

const getFormatDescription = () => {
  const descriptions = {
    1: '所有参赛队伍进行循环比赛，每队都会与其他队伍交手，最终按积分排名决定名次。',
    2: '单场淘汰制，败者直接出局，胜者晋级下一轮，直至决出冠军。',
    3: '先进行小组循环赛，各小组前几名晋级淘汰赛阶段，最终决出冠军。',
    4: '双败淘汰制，每队有两次失败机会，增加比赛的悬念和观赏性。'
  };
  
  return descriptions[props.formatType] || '根据实际情况制定的特殊赛制，具体规则请咨询组织方。';
};

const getFormatDetails = () => {
  const details = {
    1: [
      {
        icon: 'calendar',
        title: '比赛轮次',
        content: '每队与其他所有队伍各比赛一场，共进行 n-1 轮比赛（n为参赛队伍数）'
      },
      {
        icon: 'star',
        title: '排名规则',
        content: '按积分排名，积分相同时按净胜分、总得分等依次排序'
      },
      {
        icon: 'checkmarkempty',
        title: '比赛公平性',
        content: '每队比赛场次相同，充分体现各队实力水平'
      }
    ],
    2: [
      {
        icon: 'flag',
        title: '淘汰规则',
        content: '单场比赛决胜负，败者直接出局，胜者晋级下一轮'
      },
      {
        icon: 'calendar',
        title: '比赛轮次',
        content: '根据参赛队伍数确定轮次，每轮淘汰一半队伍'
      },
      {
        icon: 'star',
        title: '决赛安排',
        content: '最后两支队伍进行冠军争夺战，决出最终冠军'
      }
    ],
    3: [
      {
        icon: 'people',
        title: '小组阶段',
        content: '参赛队伍分组进行循环赛，各小组前几名晋级淘汰赛'
      },
      {
        icon: 'flag',
        title: '淘汰阶段',
        content: '晋级队伍进行单败淘汰赛，直至决出最终冠军'
      },
      {
        icon: 'star',
        title: '综合优势',
        content: '结合循环赛的公平性和淘汰赛的激烈性'
      }
    ]
  };
  
  return details[props.formatType] || [];
};

const getTimelinePhases = () => {
  if (props.config?.timeline) {
    return props.config.timeline;
  }
  
  // 默认时间线
  const defaultTimelines = {
    1: [
      { name: '报名阶段', time: '即日起至报名截止' },
      { name: '分组抽签', time: '报名结束后3天内' },
      { name: '循环赛', time: '预计2-3周' },
      { name: '颁奖典礼', time: '比赛结束后' }
    ],
    2: [
      { name: '报名阶段', time: '即日起至报名截止' },
      { name: '抽签分组', time: '报名结束后3天内' },
      { name: '淘汰赛', time: '预计1-2周' },
      { name: '决赛', time: '最后一轮' },
      { name: '颁奖典礼', time: '决赛结束后' }
    ],
    3: [
      { name: '报名阶段', time: '即日起至报名截止' },
      { name: '小组抽签', time: '报名结束后3天内' },
      { name: '小组循环赛', time: '预计1-2周' },
      { name: '淘汰赛', time: '小组赛结束后' },
      { name: '决赛', time: '最后一轮' },
      { name: '颁奖典礼', time: '决赛结束后' }
    ]
  };
  
  return defaultTimelines[props.formatType] || [];
};
</script>

<style lang="scss" scoped>
.format-section {
  .format-overview {
    margin-bottom: 30rpx;
    
    .format-title {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;
      
      text {
        margin-left: 10rpx;
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
      }
    }
    
    .format-description {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
  
  .format-details {
    margin-bottom: 30rpx;
    
    .detail-item {
      padding: 20rpx 0;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .detail-header {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .detail-title {
          margin-left: 10rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
        }
      }
      
      .detail-content {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        padding-left: 26rpx;
      }
    }
  }
  
  .scoring-rules {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    
    .rules-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 15rpx;
    }
    
    .rules-list {
      .rule-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8rpx 0;
        
        .rule-label {
          font-size: 26rpx;
          color: #666;
        }
        
        .rule-value {
          font-size: 26rpx;
          color: #4080FF;
          font-weight: 500;
        }
      }
    }
  }
  
  .timeline {
    .timeline-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .timeline-list {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 8rpx;
        top: 20rpx;
        bottom: 20rpx;
        width: 2rpx;
        background: #e8e8e8;
      }
      
      .timeline-item {
        position: relative;
        display: flex;
        align-items: flex-start;
        padding: 15rpx 0;
        
        .timeline-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background: #4080FF;
          margin-right: 20rpx;
          margin-top: 8rpx;
          position: relative;
          z-index: 1;
        }
        
        .timeline-content {
          flex: 1;
          
          .phase-name {
            display: block;
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 5rpx;
          }
          
          .phase-time {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
  }
}
</style> 