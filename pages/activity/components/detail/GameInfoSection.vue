<template>
  <InfoSection title="比赛信息" icon="info">
    <view class="info-list">
      <!-- 球服颜色 - 仅排位赛显示 -->
      <view class="info-item team-colors-item" v-if="showTeamColors">
        <view class="team-colors-header">
          <view class="info-icon">
            <uni-icons type="color" size="20" color="#666"></uni-icons>
          </view>
          <text class="info-label" v-if="userTeamAssignment">我的球队</text>
          <text class="info-label" v-else>球服颜色</text>
          <text class="info-link" @click="navigateToShop">购买球衣 ></text>
        </view>
        <view class="team-colors-content">
          <view class="team-colors-list">
            <!-- 用户已分队时只显示所在队伍 -->
            <template v-if="userTeamAssignment">
              <view class="team-color-item">
                <view class="color-dot" :style="{ backgroundColor: userTeamColor }"></view>
                <text class="team-name">{{ userTeamName }}</text>
              </view>
            </template>
            <!-- 用户未分队时显示所有队伍 -->
            <template v-else>
              <view class="team-color-item">
                <view class="color-dot" :style="{ backgroundColor: homeTeamColor || '#E74C3C' }"></view>
                <text class="team-name">{{ homeTeamName || '红队(福田赛区)' }}</text>
              </view>
              <view class="team-color-item">
                <view class="color-dot" :style="{ backgroundColor: awayTeamColor || '#3498DB' }"></view>
                <text class="team-name">{{ awayTeamName || '蓝队(香蜜湖赛区)' }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>

      <!-- 比赛时间 - 通用 -->
      <view class="info-item simple-info-item">
        <view class="info-icon">
          <uni-icons type="calendar" size="20" color="#666"></uni-icons>
        </view>
        <text class="info-label">比赛时间</text>
        <text class="info-value">{{ formatDateTime(startTime) }}</text>
      </view>

      <!-- 比赛赛制 - 通用 -->
      <view class="info-item simple-info-item">
        <view class="info-icon">
          <uni-icons type="circle" size="20" color="#666"></uni-icons>
        </view>
        <text class="info-label">比赛赛制</text>
        <text class="info-value">{{ duration || '4节，100分钟' }}</text>
      </view>

      <!-- 参赛要求 - 仅友谊赛显示 -->
      <view class="info-item simple-info-item" v-if="showTeamRequirement">
        <view class="info-icon">
          <uni-icons type="contact" size="20" color="#666"></uni-icons>
        </view>
        <text class="info-label">参赛要求</text>
        <text class="info-value">球队报名，每队{{ minPlayersPerTeam || 5 }}-{{ maxPlayersPerTeam || 15 }}人</text>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import { computed } from 'vue';
import InfoSection from './InfoSection.vue';
import dayjs from 'dayjs';
import $router from '@/sheep/router';
import { ACTIVITY_TYPE } from '@/sheep/store/activity';

const props = defineProps({
  activityType: {
    type: Number,
    required: true
  },
  startTime: {
    type: String,
    required: true
  },
  duration: {
    type: String,
    default: '4节，100分钟'
  },
  // 排位赛专用
  homeTeamColor: {
    type: String,
    default: ''
  },
  awayTeamColor: {
    type: String,
    default: ''
  },
  homeTeamName: {
    type: String,
    default: ''
  },
  awayTeamName: {
    type: String,
    default: ''
  },
  // 友谊赛专用
  minPlayersPerTeam: {
    type: Number,
    default: 5
  },
  maxPlayersPerTeam: {
    type: Number,
    default: 15
  },
  // 新增：用户分队信息
  userTeamAssignment: {
    type: String,
    default: '' // 'home' | 'away' | ''
  }
});

// 计算属性
const showTeamColors = computed(() => {
  return props.activityType === ACTIVITY_TYPE.RANKING_MATCH;
});

const showTeamRequirement = computed(() => {
  return props.activityType === ACTIVITY_TYPE.FRIENDLY_MATCH;
});

// 用户所在队伍的颜色和名称
const userTeamColor = computed(() => {
  if (!props.userTeamAssignment) return '';
  return props.userTeamAssignment === 'home' ?
    (props.homeTeamColor || '#E74C3C') :
    (props.awayTeamColor || '#3498DB');
});

const userTeamName = computed(() => {
  if (!props.userTeamAssignment) return '';
  return props.userTeamAssignment === 'home' ?
    (props.homeTeamName || '红队(福田赛区)') :
    (props.awayTeamName || '蓝队(香蜜湖赛区)');
});

// 方法
const formatDateTime = (dateTime) => {
  // 格式化日期时间，包含周几的展示
  return dayjs(dateTime).format('MM月DD日 dddd HH:mm');
};

const navigateToShop = () => {
  $router.go('/pages/goods/list', { category: 'basketball-jersey' });
};
</script>

<style lang="scss" scoped>
.info-list {
  .info-item {
    display: flex;
    align-items: center;
    padding: 5rpx 0;
    /* 优化：从30rpx减少到20rpx，减少间距 */
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .info-icon {
      margin-right: 20rpx;
    }

    .info-label {
      flex: 0 0 140rpx;
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .info-link {
      font-size: 26rpx;
      color: rgba(227, 62, 51, 0.7);
    }

    // 单行信息项优化间距
    &.simple-info-item {
      padding: 5rpx 0;
      /* 单行信息使用更紧凑的间距 */
    }

    // 球队颜色特殊布局
    &.team-colors-item {
      flex-direction: column;
      align-items: flex-start;
      padding: 5rpx 0;
      /* 优化：从25rpx减少到20rpx */

      .team-colors-header {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 16rpx;
        /* 优化：从20rpx减少到16rpx */

        .info-icon {
          margin-right: 20rpx;
        }

        .info-label {
          font-size: 28rpx;
          color: #666;
        }
      }

      .team-colors-content {
        width: 100%;
        margin-left: 40rpx;

        .team-colors-list {
          display: flex;
          flex-direction: column;
          gap: 12rpx;
          /* 优化：从16rpx减少到12rpx */
          margin-bottom: 16rpx;
          /* 优化：从20rpx减少到16rpx */

          .team-color-item {
            display: flex;
            align-items: center;
            gap: 16rpx;
            padding: 10rpx 16rpx;
            /* 优化：从12rpx 20rpx减少到10rpx 16rpx */
            // background-color: #f8f9fa;
            border-radius: 8rpx;

            .color-dot {
              width: 28rpx;
              height: 28rpx;
              border-radius: 50%;
              border: 2rpx solid #ddd;
              flex-shrink: 0;
            }

            .team-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 500;
            }

            .user-indicator {
              font-size: 24rpx;
              color: #4CAF50;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
</style>