<template>
  <InfoSection :title="title" :icon="icon" :collapsible="true" :initiallyExpanded="initiallyExpanded">
    <view class="notice-section">
      <view class="notice-content" v-if="isHTML" v-html="content"></view>
      <view class="notice-list" v-else>
        <view 
          class="notice-item" 
          v-for="(item, index) in items" 
          :key="index">
          <view class="item-bullet">•</view>
          <view class="item-text">{{ item }}</view>
        </view>
        <view class="notice-text" v-if="!items || items.length === 0">
          {{ content || '暂无内容' }}
        </view>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import { computed } from 'vue';
import InfoSection from './InfoSection.vue';

const props = defineProps({
  title: {
    type: String,
    default: '报名须知'
  },
  icon: {
    type: String,
    default: 'info'
  },
  content: {
    type: String,
    default: ''
  },
  items: {
    type: Array,
    default: () => []
  },
  initiallyExpanded: {
    type: Boolean,
    default: false
  }
});

// 是否为HTML内容
const isHTML = computed(() => {
  return props.content && (props.content.includes('<') && props.content.includes('>'));
});
</script>

<style lang="scss" scoped>
.notice-section {
  .notice-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
  
  .notice-list {
    .notice-item {
      display: flex;
      margin-bottom: 20rpx;
      
      .item-bullet {
        font-size: 28rpx;
        color: #999;
        margin-right: 10rpx;
        line-height: 1.6;
      }
      
      .item-text {
        flex: 1;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
    
    .notice-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}
</style> 