<template>
  <view class="team-card">
    <!-- 队伍标题栏 -->
    <view 
      class="team-header"
      :style="{ backgroundColor: hexToRgba(team.color || defaultTeamColor, 0.85) }"
      @click="$emit('team-click', team)" 
    >
      <image 
        class="header-team-logo" 
        :src="getTeamLogo(team)" 
        mode="aspectFill"
        @error="handleImageError"
      />
      <view class="header-team-info">
        <text class="team-name">{{ team.name || '球队名称' }}</text>
        <view class="team-meta">
          <text class="team-count">({{ getPlayerCount(team) }}/{{ getMaxPlayers(team) }})</text>
          <text class="team-status" v-if="!isQualified">还需{{ minPlayers - getPlayerCount(team) }}人</text>
        </view>
      </view>
      <view class="team-status-indicator">
        <uni-icons 
          v-if="isQualified" 
          type="checkmarkempty" 
          size="18" 
          color="#fff"
        />
        <uni-icons 
          v-else 
          type="info" 
          size="18" 
          color="#fff"
        />
      </view>
    </view>
    
    <!-- 队伍成员/队长信息 -->
    <view class="team-content">
      <!-- 友谊赛格式：显示球员头像列表 -->
      <view v-if="team.players && team.players.length > 0" class="players-section">
        <scroll-view scroll-x="true" class="team-player-scroll">
          <view 
            class="team-player-wrapper" 
            v-for="player in team.players" 
            :key="'player-'+player.id"
            @click="$emit('player-click', player)"
          >
            <image 
              class="team-player-avatar" 
              :src="getPlayerAvatar(player)" 
              mode="aspectFill"
              @error="handleAvatarError"
            />
            <text class="team-player-name">{{ player.nickname || '球员' }}</text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 联赛格式：显示队长信息 -->
      <view v-else-if="team.captain" class="captain-section">
        <view class="captain-info">
          <text class="captain-label">队长：</text>
          <text class="captain-name">{{ team.captain.name }}</text>
        </view>
        <view class="member-count">
          <text>共{{ getPlayerCount(team) }}名队员</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-players">
        <text>该队暂无球员信息</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import sheep from '@/sheep';

// 定义组件属性
const props = defineProps({
  team: {
    type: Object,
    required: true
  },
  minPlayers: {
    type: Number,
    default: 5
  },
  maxPlayersPerTeam: {
    type: Number,
    default: 15 // 默认最大15人
  },
  isQualified: {
    type: Boolean,
    default: false
  }
});

// 定义组件事件
const emit = defineEmits(['team-click', 'player-click']);

// 默认颜色
const defaultColors = ['#b7a99a', '#a8c8c0', '#d1c6c0', '#c4b6c4', '#b7c9d7'];

// 计算属性
const defaultTeamColor = computed(() => {
  const teamId = props.team.teamId || props.team.id || 0;
  const index = teamId % defaultColors.length;
  return defaultColors[index];
});

// 方法
const getTeamLogo = (team) => {
  const logoUrl = team.logoUrl || team.logo;
  if (logoUrl) {
    return logoUrl.startsWith('http') ? logoUrl : sheep.$url.cdn(logoUrl);
  }
  return '';
};

const getPlayerAvatar = (player) => {
  if (player.avatar) {
    return player.avatar.startsWith('http') ? player.avatar : sheep.$url.cdn(player.avatar);
  }
  return '';
};

const getPlayerCount = (team) => {
  if (team.memberCount !== undefined) {
    return team.memberCount; // 联赛格式
  }
  return team.players?.length || 0; // 友谊赛格式
};

const getMaxPlayers = (team) => {
  return props.maxPlayersPerTeam;
};

// 图片错误处理
const handleImageError = (e) => {
  console.warn('队伍logo加载失败');
  e.target.src = '';
};

const handleAvatarError = (e) => {
  console.warn('球员头像加载失败');
  e.target.src = '';
};

// Helper: 将 HEX 颜色转换为 RGBA
function hexToRgba(hex, alpha = 1) {
  if (!hex) return `rgba(74, 144, 226, ${alpha})`; // 默认蓝色
  
  // 移除 # 号
  hex = hex.replace('#', '');
  
  // 处理缩写形式 (e.g., #03F)
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }
  
  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}
</script>

<style lang="scss" scoped>
// 基础颜色变量
$success-color: #52c41a;
$warning-color: #ff9800;
$border-color: #f0f0f0;
$text-color: #333333;
$text-secondary: #999999;

.team-card {
  border-radius: 12rpx;
  overflow: hidden;
  border: 1px solid $border-color;
  background-color: #fff;
  transition: all 0.3s ease;
}

// 队伍标题栏
.team-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  color: white;
  position: relative;
  cursor: pointer;
  min-height: 70rpx;
  
  &:active {
    opacity: 0.8;
  }
}

.header-team-logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-team-info {
  flex: 1;
  
  .team-name {
    display: block;
    font-size: 30rpx;
    font-weight: 600;
    margin-bottom: 2rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
  }
  
  .team-meta {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 24rpx;
    opacity: 0.9;
    line-height: 1;
  }
  
  .team-count {
    font-size: 24rpx;
  }
  
  .team-status {
    font-size: 24rpx;
  }
}

.team-status-indicator {
  flex-shrink: 0;
}

// 队伍内容区域
.team-content {
  padding: 16rpx;
}

// 球员区域
.players-section {
  border-radius: 8rpx;
  
  .team-player-scroll {
    white-space: nowrap;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  .team-player-wrapper {
    display: inline-block;
    text-align: center;
    margin-right: 24rpx;
    vertical-align: top;
    width: 110rpx;
    cursor: pointer;
    
    &:last-child {
      margin-right: 0;
    }
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .team-player-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin-bottom: 8rpx;
    border: 2rpx solid #fff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  .team-player-name {
    display: block;
    font-size: 24rpx;
    color: $text-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

// 队长区域
.captain-section {
  .captain-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    
    .captain-label {
      font-size: 28rpx;
      color: $text-secondary;
    }
    
    .captain-name {
      font-size: 28rpx;
      color: $text-color;
      font-weight: 500;
      margin-left: 8rpx;
    }
  }
  
  .member-count {
    font-size: 26rpx;
    color: $text-secondary;
  }
}

// 空状态
.empty-players {
  padding: 30rpx 0;
  text-align: center;
  color: $text-secondary;
  font-size: 26rpx;
}
</style> 