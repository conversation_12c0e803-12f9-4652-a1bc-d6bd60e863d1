<template>
  <InfoSection title="费用明细" icon="wallet" :collapsible="true" :initiallyExpanded="false">
    <view class="fee-detail-content">
      <view class="fee-total-item">
        <view class="fee-total-header">
          <view class="fee-total-label">
            <uni-icons type="wallet-filled" size="18" color="#4a90e2"></uni-icons>
            <text>{{ getFeeTitle() }}</text>
          </view>
          <view class="fee-total-value">¥{{ formatPrice(getDisplayFee()) }}</view>
        </view>
        <view class="fee-total-breakdown">
          <view class="fee-breakdown-item">
            <view class="fee-breakdown-item-content">
              <view class="fee-breakdown-item-header">
                <view class="fee-breakdown-label">
                  <uni-icons type="circle-filled" size="16" color="#666"></uni-icons>
                  <text>场地费用</text>
                </view>
                <view class="fee-breakdown-value">¥{{ formatPrice(venueFeeAmount) }}</view>
              </view>
              <view class="fee-description">
                {{ venueFeeDescription || '2小时CBA标准篮球场（全场）预定费' }}
              </view>
            </view>
          </view>
          <view class="fee-breakdown-item">
            <view class="fee-breakdown-item-content">
              <view class="fee-breakdown-item-header">
                <view class="fee-breakdown-label">
                  <uni-icons type="person-filled" size="16" color="#666"></uni-icons>
                  <text>比赛配套</text>
                </view>
                <view class="fee-breakdown-value">¥{{ formatPrice(supportFeeAmount) }}</view>
              </view>
              <view class="fee-description">
                {{ supportFeeDescription || '包含1名裁判员（¥260）、1名记分计时员（¥100）、比赛专用电子屏（限时免费）、24秒计时器（限时免费）、AI自动跟拍设备（限时免费）、苹果15pro录像设备（限时免费）、全场视频回放（限时免费）、进球集锦（限时免费）、数据统计（限时免费）' }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import InfoSection from './InfoSection.vue';

const props = defineProps({
  totalFee: {
    type: Number,
    default: 0
  },
  venueFeeAmount: {
    type: Number,
    default: 80000 // 默认800元 (单位分)
  },
  venueFeeDescription: {
    type: String,
    default: '2小时CBA标准篮球场（全场）预定费'
  },
  supportFeeAmount: {
    type: Number,
    default: 36000 // 默认360元 (单位分)
  },
  supportFeeDescription: {
    type: String,
    default: '包含1名裁判员（¥260）、1名记分计时员（¥100）、比赛专用电子屏（限时免费）、24秒计时器（限时免费）、AI自动跟拍设备（限时免费）、苹果15pro录像设备（限时免费）、全场视频回放（限时免费）、进球集锦（限时免费）、数据统计（限时免费）'
  },
  feeType: {
    type: String,
    default: 'individual', // 'individual' | 'team' | 'perPlayer'
    validator: (value) => ['individual', 'team', 'perPlayer'].includes(value)
  },
  teamFee: {
    type: Number,
    default: 0
  },
  leagueFeePerPlayer: {
    type: Number,
    default: 0
  }
});

const formatPrice = (price) => {
  if (typeof price !== 'number') return '0';
  return (Number(price) / 100).toFixed(0);
};

// 根据费用类型获取费用标题
const getFeeTitle = () => {
  switch (props.feeType) {
    case 'individual':
      return '赛事总费用';
    case 'team':
      return '每队需支付费用';
    case 'perPlayer':
      return '每人报名费用';
    default:
      return '费用详情';
  }
};

// 根据费用类型获取要显示的费用
const getDisplayFee = () => {
  switch (props.feeType) {
    case 'individual':
      return props.totalFee || 0;
    case 'team':
      return props.teamFee || 0;
    case 'perPlayer':
      return props.leagueFeePerPlayer || 0;
    default:
      return props.totalFee || 0;
  }
};

</script>

<style lang="scss" scoped>
/* Styles moved from detail.vue */
.fee-detail-content {
  padding: 20rpx 0; // 内边距
}
.fee-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin: 15rpx 0 10rpx;
  padding-bottom: 16rpx;
}
.fee-total-item {
  background: #F8FAFC;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 0; // Adjusted margin as it's the only content now
}
.fee-total-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.fee-total-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.fee-total-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #f59520;
}
.fee-total-breakdown {
  background: #fff;
  border-radius: 16rpx;
  padding: 10rpx 20rpx;
  margin-top: 10rpx;
}
.fee-breakdown-item {
  padding: 16rpx 0;
  border-bottom: 1px solid #f5f5f5;
  &:last-child {
    border-bottom: none;
  }
  .fee-breakdown-item-content {
    display: flex;
    flex-direction: column;
  }
  .fee-breakdown-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}
.fee-breakdown-label {
  font-size: 26rpx;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.fee-breakdown-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}
.fee-description {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.5;
  margin-top: 8rpx;
  padding-left: 40rpx;
}
</style> 