<template>
  <view class="room-container" :class="{ 'simple-mode': mode === 'simple' }">
    <view class="room-header">
      <view class="room-title">
        <uni-icons type="staff-filled" size="20" :color="mode === 'simple' ? '#4A90E2' : '#fff'"></uni-icons>
        <span>{{ mode === 'simple' ? '好友组队信息' : '好友组队房间' }}</span>
      </view>
      <view class="room-timer" v-if="countdownTime && mode !== 'simple'">
        <uni-icons type="pyq" size="14" color="#fff"></uni-icons>
        <span>{{ countdownTime }}</span>
      </view>
      <view class="room-status" v-if="mode === 'simple'" :class="{ 'status-waiting': roomData?.status === 1 }">
        {{ roomData?.status === 1 ? '组队中' : '已确认' }}
      </view>
    </view>

    <view class="room-content">
      <view class="room-info" v-if="mode !== 'simple'">
        房间创建成功！你可以邀请最多2位好友加入组队，系统会优先将房间内的球员分到同一队。房间将在{{ Math.floor(initialDuration / 60) }}分钟内自动关闭，请尽快邀请好友加入。
      </view>

      <!-- 成员卡片展示（通用于simple和detail模式） -->
      <view class="room-members">
        <!-- 房主 -->
        <view class="member-slot" v-if="roomData.owner">
          <view class="member-avatar">
            <image v-if="roomData.owner.avatar" :src="roomData.owner.avatar" mode="aspectFill"></image>
            <uni-icons v-else type="person-filled" size="30" color="#c0c4cc"></uni-icons>
          </view>
          <view class="member-name">{{ roomData.owner.nickname || '房主' }}</view>
          <view class="member-role">房主</view>
        </view>

        <!-- 已邀请成员 -->
        <view class="member-slot" v-for="(member, index) in roomData.members" :key="member.userId">
          <view class="member-avatar">
            <image v-if="member.avatar" :src="member.avatar" mode="aspectFill"></image>
            <uni-icons v-else type="person-filled" size="30" color="#c0c4cc"></uni-icons>
          </view>
          <view class="member-name">{{ member.nickname || `队员${index + 1}` }}</view>
          <view class="member-role">队员</view>
        </view>

        <!-- 空位 -->
        <view class="member-slot" v-for="n in emptySlots" :key="'empty-' + n">
          <!-- detail模式：可点击分享的空位 -->
          <button v-if="mode !== 'simple' && canInvite" class="member-avatar add-member" open-type="share"
            @click="handleInvite">
            <span class="plus-sign">+</span>
          </button>
          <!-- simple模式或不能邀请：普通空位显示 -->
          <view v-else class="member-avatar empty-slot">
            <uni-icons type="person" size="30" color="#c0c4cc"></uni-icons>
          </view>
          <view class="member-name">{{ mode === 'simple' ? '空位' : '待邀请' }}</view>
          <view class="member-role">&nbsp;</view>
        </view>
      </view>

      <!-- 邀请按钮：仅在detail模式下显示 -->
      <button class="invite-button" open-type="share" @click="handleInvite" v-if="canInvite && mode !== 'simple'">
        <uni-icons type="paperplane-filled" size="18" color="#fff" style="margin-right: 4px;"></uni-icons>
        <span>邀请好友加入</span>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  roomData: {
    type: Object,
    required: true,
    default: () => ({
      owner: { userId: null, nickname: '房主', avatar: null },
      members: [], // { userId, nickname, avatar }
      expireTime: null // Unix timestamp in seconds
    })
  },
  mode: {
    type: String,
    default: 'detail', // 'detail' 或 'simple'
    validator: (value) => ['detail', 'simple'].includes(value)
  }
});

const emit = defineEmits(['invite']);

const countdownTime = ref('');
const timer = ref(null);
const initialDuration = ref(30 * 60); // 默认30分钟，单位秒

// 计算空位数
const emptySlots = computed(() => {
  const maxMembers = 3; // 房主 + 2个队员
  const currentMemberCount = 1 + (props.roomData.members?.length || 0);
  return Math.max(0, maxMembers - currentMemberCount);
});

// 是否还能邀请
const canInvite = computed(() => {
  // 检查是否有空位
  if (emptySlots.value <= 0) {
    return false;
  }

  // 检查房间是否过期
  if (props.roomData.expireTime) {
    const now = Math.floor(Date.now() / 1000);
    if (now >= props.roomData.expireTime) {
      return false; // 房间已过期，不能邀请
    }
  }

  return true;
});

// 格式化倒计时
const formatTime = (seconds) => {
  if (seconds <= 0) return '00:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
};

// 启动倒计时
const startCountdown = () => {
  if (timer.value) {
    clearInterval(timer.value);
  }
  if (!props.roomData.expireTime) {
    countdownTime.value = formatTime(initialDuration.value); // 如果没有过期时间，显示初始时长
    return;
  }

  const now = Math.floor(Date.now() / 1000);
  let remainingSeconds = props.roomData.expireTime - now;

  // 如果提供了过期时间，则覆盖初始时长
  initialDuration.value = remainingSeconds > 0 ? remainingSeconds : initialDuration.value;

  if (remainingSeconds <= 0) {
    countdownTime.value = '00:00';
    return;
  }

  countdownTime.value = formatTime(remainingSeconds);

  timer.value = setInterval(() => {
    remainingSeconds--;
    if (remainingSeconds <= 0) {
      clearInterval(timer.value);
      countdownTime.value = '00:00';
      // 可以触发一个事件通知父组件房间已到期
      // emit('room-expired'); 
    } else {
      countdownTime.value = formatTime(remainingSeconds);
    }
  }, 1000);
};

// 处理邀请按钮点击
const handleInvite = () => {
  emit('invite');
};

// 监听 roomData.expireTime 变化，重新启动倒计时
watch(() => props.roomData.expireTime, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    startCountdown();
  }
});

// 新增计算属性：统一处理成员数据
const allMembers = computed(() => {
  const members = [];
  if (props.roomData.owner) {
    members.push(props.roomData.owner);
  }
  if (props.roomData.members) {
    members.push(...props.roomData.members);
  }
  return members;
});

const currentMemberCount = computed(() => {
  return allMembers.value.length;
});

const maxMembers = computed(() => {
  return props.roomData.maxMembers || 3;
});

onMounted(() => {
  startCountdown();
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>

<style lang="scss" scoped>
.room-container {
  margin: 30rpx 0;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);

  &.simple-mode {
    margin: 0 0 20rpx 0;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    .room-header {
      background-color: transparent;
      color: #333;
      padding: 0 0 16rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      margin-bottom: 16rpx;

      .room-title {
        color: #333;
        font-size: 28rpx;
        font-weight: 600;
      }

      .room-status {
        background-color: #f0f8ff;
        color: #4a90e2;
        padding: 4rpx 10rpx;
        border-radius: 10rpx;
        font-size: 20rpx;

        &.status-waiting {
          background-color: #fff7e6;
          color: #fa8c16;
        }
      }
    }

    .room-content {
      padding: 0;
    }


  }
}

.room-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #4a90e2;
  color: white;
}

.room-title {
  font-weight: 500;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.room-timer {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.room-content {
  padding: 30rpx;
}

.room-info {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #909399;
  line-height: 1.5;
}

.room-members {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.member-slot {
  width: calc(33.33% - 20rpx * 2 / 3);
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.member-avatar {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
  position: relative;
  overflow: hidden;

  image {
    width: 100%;
    height: 100%;
  }
}

.member-name {
  font-size: 28rpx;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  margin-bottom: 4rpx;
}

.member-role {
  font-size: 24rpx;
  color: #909399;
}

.add-member {
  border: 2px dashed #dcdfe6;
  background-color: #f8f8f8;
  cursor: pointer;
  position: relative;

  /* 重置button样式 */
  &[open-type="share"] {
    padding: 0;
    margin: 0;
    outline: none;
    border-radius: 50%;
    font-size: inherit;
    line-height: inherit;

    &::after {
      border: none;
      background: none;
    }

    &:active {
      opacity: 0.7;
      transform: scale(0.95);
    }
  }
}

.empty-slot {
  border: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  color: #c0c4cc;
}

.plus-sign {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -58%);
  font-size: 80rpx;
  color: #c0c4cc;
  font-weight: 300;
  line-height: 1;
}

.invite-button {
  display: flex;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  align-items: center;
  justify-content: center;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  cursor: pointer;
  text-align: center;
  padding: 0;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);

  &:active {
    opacity: 0.8;
  }
}

.room-status {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: white;
  margin-left: 10rpx;

  &.status-waiting {
    background-color: rgba(255, 255, 255, 0.2);
  }
}
</style>