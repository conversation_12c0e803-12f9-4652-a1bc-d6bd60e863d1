<template>
  <view class="signup-options">
    <view class="options-list">
      <!-- 个人报名 -->
      <view 
        class="option-item" 
        :class="{ 'selected': selectedMode === 1 }"
        @click="selectMode(1)"
      >
        <view class="option-radio">
          <view v-if="selectedMode === 1" class="radio-selected">
            <view class="radio-inner"></view>
          </view>
          <view v-else class="radio-unselected"></view>
        </view>
        <view class="option-content">
          <view class="option-title">球员个人报名</view>
          <view class="option-desc">系统将根据报名球员战力值均衡分配队伍，确保比赛公平性</view>
        </view>
      </view>
      
      <!-- 好友组队 -->
      <view 
        class="option-item" 
        :class="{ 
          'selected': selectedMode === 2,
          'disabled': !isFriendGroupAvailable
        }"
        @click="selectMode(2)"
      >
        <view class="option-radio">
          <view v-if="selectedMode === 2" class="radio-selected">
            <view class="radio-inner"></view>
          </view>
          <view v-else class="radio-unselected"></view>
        </view>
        <view class="option-content">
          <view class="option-title">好友组队报名</view>
          <view class="option-desc" v-if="isFriendGroupAvailable">在确保参赛球队实力均衡的前提下，系统将优先将你和好友分配至同一队伍</view>
          <view class="option-desc unavailable" v-else>好友组队已满，请选择个人报名</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  selectedMode: {
    type: Number,
    default: 1
  },
  isFriendGroupAvailable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['change']);

// 选择报名方式
const selectMode = (mode) => {
  // 如果好友组队不可用，禁止选择
  if (mode === 2 && !props.isFriendGroupAvailable) {
    return;
  }
  
  if (mode !== props.selectedMode) {
    emit('change', mode);
  }
};
</script>

<style lang="scss" scoped>
.signup-options {
  padding: 0;
  padding-top: 20rpx;
  .options-title {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 30rpx;
  }
  
  .options-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    
    .option-item {
      display: flex;
      align-items: flex-start;
      padding: 30rpx 24rpx;
      border-radius: 12rpx;
      border: 1px solid #EAECF0;
      transition: all 0.3s ease;
      
      &.selected {
        border-color: #4080FF;
        
        .option-title {
          color: #4080FF;
          font-weight: 600;
        }
        
        .option-desc {
          color: #666;
        }
        
        .option-radio {
          .radio-selected {
            border-color: #4080FF;
            background-color: #FFFFFF;
            
            .radio-inner {
              background-color: #4080FF;
            }
          }
        }
      }
      
      &.disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
      
      .option-radio {
        margin-right: 24rpx;
        margin-top: 6rpx;
        
        .radio-selected, .radio-unselected {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          border: 2rpx solid #CCCCCC;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
        }
        
        .radio-selected {
          border-color: #4080FF;
          background-color: #FFFFFF;
          
          .radio-inner {
            width: 24rpx;
            height: 24rpx;
            border-radius: 50%;
            background-color: #4080FF;
          }
        }
        
        .radio-unselected {
          border-color: #CCCCCC;
          background-color: #FFFFFF;
        }
      }
      
      .option-content {
        flex: 1;
        
        .option-title {
          font-size: 32rpx;
          color: #333333;
          font-weight: 500;
          margin-bottom: 10rpx;
        }
        
        .option-desc {
          font-size: 26rpx;
          color: #999999;
          line-height: 1.5;
          
          &.unavailable {
            color: #FF9800;
          }
        }
      }
    }
  }
}
</style> 