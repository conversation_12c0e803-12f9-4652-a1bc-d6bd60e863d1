<template>
  <view class="activity-progress-section">
    <!-- 排位赛进度 -->
    <ActivitySignupProgress 
      v-if="activityType === ACTIVITY_TYPE.RANKING_MATCH"
      :currentPlayers="currentPlayers"
      :maxPlayers="maxPlayers" 
      :waitlistPlayers="waitlistPlayers"
      :homeTeamPlayers="homeTeamPlayers"
      :awayTeamPlayers="awayTeamPlayers" 
      :homeTeamColor="homeTeamColor"
      :awayTeamColor="awayTeamColor" 
      :homeTeamName="homeTeamName"
      :awayTeamName="awayTeamName" 
    />

    <!-- 友谊赛进度 - 使用统一组件 -->
    <TeamProgressDisplay 
      v-else-if="activityType === ACTIVITY_TYPE.FRIENDLY_MATCH"
      title="已报名球队"
      :currentTeams="currentTeams"
      :maxTeams="maxTeams" 
      :signedUpTeams="signedUpTeams"
      :minPlayersPerTeam="minPlayersPerTeam || 5"
      :maxPlayersPerTeam="maxPlayersPerTeam || 15"
    />

    <!-- 联赛进度 - 使用统一组件 -->
    <TeamProgressDisplay 
      v-else-if="activityType === ACTIVITY_TYPE.LEAGUE"
      title="参赛队伍"
      :currentTeams="currentTeams"
      :maxTeams="maxTeams"
      :signedUpTeams="signedUpTeams"
      :minPlayersPerTeam="minPlayersPerTeam || 5"
      :maxPlayersPerTeam="maxPlayersPerTeam || 15"
    />
  </view>
</template>

<script setup>
import { ACTIVITY_TYPE } from '@/sheep/store/activity';
import ActivitySignupProgress from './ActivitySignupProgress.vue';
import TeamProgressDisplay from './TeamProgressDisplay.vue';

const props = defineProps({
  activityType: {
    type: Number,
    required: true
  },
  // 排位赛专用
  currentPlayers: {
    type: Number,
    default: 0
  },
  maxPlayers: {
    type: Number,
    default: 20
  },
  waitlistPlayers: {
    type: Array,
    default: () => []
  },
  homeTeamPlayers: {
    type: Array,
    default: () => []
  },
  awayTeamPlayers: {
    type: Array,
    default: () => []
  },
  homeTeamColor: {
    type: String,
    default: ''
  },
  awayTeamColor: {
    type: String,
    default: ''
  },
  homeTeamName: {
    type: String,
    default: ''
  },
  awayTeamName: {
    type: String,
    default: ''
  },
  // 友谊赛/联赛通用
  currentTeams: {
    type: Number,
    default: 0
  },
  maxTeams: {
    type: Number,
    default: 2
  },
  signedUpTeams: {
    type: Array,
    default: () => []
  },
  // 联赛专用
  minTeams: {
    type: Number,
    default: 4
  },
  // 新增：每队最小人数（用于分组显示）
  minPlayersPerTeam: {
    type: Number,
    default: 5
  },
  // 新增：每队最大人数（用于分组显示）
  maxPlayersPerTeam: {
    type: Number,
    default: 15
  }
});
</script>

<style lang="scss" scoped>
.activity-progress-section {
  // 样式由子组件自己管理
}
</style> 