<template>
  <InfoSection title="联赛信息" icon="info">
    <view class="league-info">
      <view class="info-list">
        <view class="info-item">
          <view class="info-icon">
            <uni-icons type="calendar" size="20" color="#666"></uni-icons>
          </view>
          <text class="info-label">联赛时间</text>
          <text class="info-value">{{ formatLeagueTime() }}</text>
        </view>
        <view class="info-item">
          <view class="info-icon">
            <uni-icons type="circle" size="20" color="#666"></uni-icons>
          </view>
          <text class="info-label">赛制类型</text>
          <text class="info-value">{{ getFormatTypeName() }}</text>
        </view>
        <view class="info-item">
          <view class="info-icon">
            <uni-icons type="people" size="20" color="#666"></uni-icons>
          </view>
          <text class="info-label">参赛要求</text>
          <text class="info-value">球队报名，每队{{ minPlayersPerTeam || 8 }}-{{ maxPlayersPerTeam || 15 }}人</text>
        </view>
        <view class="info-item" v-if="leagueInfo?.duration">
          <view class="info-icon">
            <uni-icons type="clock" size="20" color="#666"></uni-icons>
          </view>
          <text class="info-label">联赛周期</text>
          <text class="info-value">{{ leagueInfo.duration }}</text>
        </view>
      </view>
      
      <view class="league-highlights" v-if="hasHighlights">
        <text class="highlights-title">联赛特色</text>
        <view class="highlights-list">
          <view class="highlight-item" v-if="leagueInfo?.formatType === 1">
            <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
            <text>循环赛制，每队都有充分比赛机会</text>
          </view>
          <view class="highlight-item" v-if="leagueInfo?.formatType === 2">
            <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
            <text>单败淘汰，激烈竞争决出冠军</text>
          </view>
          <view class="highlight-item" v-if="leagueInfo?.config?.hasRanking">
            <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
            <text>实时积分排名，荣誉榜单</text>
          </view>
          <view class="highlight-item" v-if="leagueInfo?.config?.hasAwards">
            <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
            <text>丰厚奖品，冠亚季军都有奖</text>
          </view>
          <view class="highlight-item">
            <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
            <text>专业裁判，公平公正比赛环境</text>
          </view>
        </view>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import { computed } from 'vue';
import InfoSection from './InfoSection.vue';
import dayjs from 'dayjs';

const props = defineProps({
  leagueInfo: {
    type: Object,
    default: () => ({})
  },
  basicInfo: {
    type: Object,
    default: () => ({})
  },
  minPlayersPerTeam: {
    type: Number,
    default: 8
  },
  maxPlayersPerTeam: {
    type: Number,
    default: 15
  }
});

// 计算属性
const hasHighlights = computed(() => {
  return props.leagueInfo?.formatType || props.leagueInfo?.config;
});

// 方法
const formatLeagueTime = () => {
  if (!props.basicInfo?.startTime || !props.basicInfo?.endTime) {
    return '待定';
  }
  
  const startTime = dayjs(props.basicInfo.startTime);
  const endTime = dayjs(props.basicInfo.endTime);
  
  if (startTime.isSame(endTime, 'day')) {
    return startTime.format('MM月DD日');
  }
  
  return `${startTime.format('MM月DD日')} - ${endTime.format('MM月DD日')}`;
};

const getFormatTypeName = () => {
  if (!props.leagueInfo?.formatType) return '待定';
  
  const formatTypes = {
    1: '循环赛',
    2: '单败淘汰赛',
    3: '小组循环+淘汰赛',
    4: '双败淘汰赛'
  };
  
  return formatTypes[props.leagueInfo.formatType] || '自定义赛制';
};
</script>

<style lang="scss" scoped>
.league-info {
  .info-list {
    .info-item {
      display: flex;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-icon {
        margin-right: 20rpx;
      }
      
      .info-label {
        flex: 0 0 140rpx;
        font-size: 28rpx;
        color: #666;
      }
      
      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
    }
  }
  
  .league-highlights {
    margin-top: 30rpx;
    padding-top: 30rpx;
    border-top: 1px solid #f5f5f5;
    
    .highlights-title {
      display: block;
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .highlights-list {
      .highlight-item {
        display: flex;
        align-items: center;
        padding: 12rpx 0;
        
        text {
          margin-left: 12rpx;
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }
}
</style> 