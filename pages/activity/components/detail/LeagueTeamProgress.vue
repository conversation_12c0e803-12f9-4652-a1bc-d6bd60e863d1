<template>
  <InfoSection title="参赛队伍" icon="people">
    <view class="league-progress">
      <view class="progress-header">
        <view class="progress-stats">
          <text class="current-count">{{ currentTeams }}</text>
          <text class="separator">/</text>
          <text class="max-count">{{ maxTeams }}</text>
          <text class="unit">支队伍</text>
        </view>
        <view class="progress-status" :class="statusClass">
          <text>{{ statusText }}</text>
        </view>
      </view>
      
      <view class="progress-bar">
        <view class="progress-track">
          <view 
            class="progress-fill" 
            :style="{ width: progressPercentage + '%' }"
          ></view>
          <view 
            class="min-threshold" 
            :style="{ left: minThresholdPercentage + '%' }"
            v-if="minTeams > 0"
          >
            <text class="threshold-label">最少{{ minTeams }}队</text>
          </view>
        </view>
      </view>
      
      <view class="teams-list" v-if="signedUpTeams && signedUpTeams.length > 0">
        <text class="list-title">已报名队伍</text>
        <view class="teams-grid">
          <view 
            class="team-card" 
            v-for="team in signedUpTeams" 
            :key="team.teamId"
          >
            <view class="team-header">
              <image 
                class="team-logo" 
                :src="team.logo || '/static/images/default-team-logo.png'"
                mode="aspectFill"
              />
              <view class="team-info">
                <text class="team-name">{{ team.name }}</text>
                <text class="team-members">{{ team.memberCount || 0 }}人</text>
              </view>
            </view>
            <view class="team-captain" v-if="team.captain">
              <text class="captain-label">队长：</text>
              <text class="captain-name">{{ team.captain.name }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else>
        <uni-icons type="people" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">暂无队伍报名</text>
        <text class="empty-hint">成为第一支报名的队伍吧！</text>
      </view>
      
      <view class="progress-tips">
        <view class="tip-item" v-if="remainingSlots > 0">
          <uni-icons type="info" size="16" color="#4080FF"></uni-icons>
          <text>还需 {{ remainingSlots }} 支队伍即可开赛</text>
        </view>
        <view class="tip-item" v-if="isNearFull">
          <uni-icons type="notification" size="16" color="#FF9800"></uni-icons>
          <text>名额紧张，仅剩 {{ remainingSlots }} 个名额</text>
        </view>
        <view class="tip-item" v-if="isFull">
          <uni-icons type="checkmarkempty" size="16" color="#52c41a"></uni-icons>
          <text>报名已满，联赛即将开始</text>
        </view>
      </view>
    </view>
  </InfoSection>
</template>

<script setup>
import { computed } from 'vue';
import InfoSection from './InfoSection.vue';

const props = defineProps({
  currentTeams: {
    type: Number,
    default: 0
  },
  minTeams: {
    type: Number,
    default: 4
  },
  maxTeams: {
    type: Number,
    default: 16
  },
  signedUpTeams: {
    type: Array,
    default: () => []
  }
});

// 计算属性
const progressPercentage = computed(() => {
  if (props.maxTeams === 0) return 0;
  return Math.min((props.currentTeams / props.maxTeams) * 100, 100);
});

const minThresholdPercentage = computed(() => {
  if (props.maxTeams === 0) return 0;
  return (props.minTeams / props.maxTeams) * 100;
});

const remainingSlots = computed(() => {
  return Math.max(0, props.maxTeams - props.currentTeams);
});

const isNearFull = computed(() => {
  return remainingSlots.value <= 2 && remainingSlots.value > 0;
});

const isFull = computed(() => {
  return props.currentTeams >= props.maxTeams;
});

const canStart = computed(() => {
  return props.currentTeams >= props.minTeams;
});

const statusText = computed(() => {
  if (isFull.value) return '报名已满';
  if (canStart.value) return '可以开赛';
  return '招募中';
});

const statusClass = computed(() => {
  if (isFull.value) return 'status-full';
  if (canStart.value) return 'status-ready';
  return 'status-recruiting';
});
</script>

<style lang="scss" scoped>
.league-progress {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .progress-stats {
      display: flex;
      align-items: baseline;
      
      .current-count {
        font-size: 48rpx;
        font-weight: bold;
        color: #4080FF;
      }
      
      .separator {
        font-size: 32rpx;
        color: #999;
        margin: 0 8rpx;
      }
      
      .max-count {
        font-size: 32rpx;
        color: #666;
      }
      
      .unit {
        font-size: 28rpx;
        color: #666;
        margin-left: 8rpx;
      }
    }
    
    .progress-status {
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      
      &.status-recruiting {
        background: #e6f7ff;
        color: #1890ff;
      }
      
      &.status-ready {
        background: #f6ffed;
        color: #52c41a;
      }
      
      &.status-full {
        background: #fff2e8;
        color: #fa8c16;
      }
    }
  }
  
  .progress-bar {
    margin-bottom: 30rpx;
    
    .progress-track {
      position: relative;
      height: 12rpx;
      background: #f0f0f0;
      border-radius: 6rpx;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4080FF 0%, #40a9ff 100%);
        border-radius: 6rpx;
        transition: width 0.3s ease;
      }
      
      .min-threshold {
        position: absolute;
        top: -30rpx;
        transform: translateX(-50%);
        
        &::before {
          content: '';
          position: absolute;
          top: 30rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 2rpx;
          height: 12rpx;
          background: #52c41a;
        }
        
        .threshold-label {
          font-size: 20rpx;
          color: #52c41a;
          white-space: nowrap;
        }
      }
    }
  }
  
  .teams-list {
    margin-bottom: 30rpx;
    
    .list-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .teams-grid {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      
      .team-card {
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 1px solid #e8e8e8;
        
        .team-header {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;
          
          .team-logo {
            width: 60rpx;
            height: 60rpx;
            border-radius: 8rpx;
            margin-right: 20rpx;
          }
          
          .team-info {
            flex: 1;
            
            .team-name {
              display: block;
              font-size: 30rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 5rpx;
            }
            
            .team-members {
              font-size: 24rpx;
              color: #666;
            }
          }
        }
        
        .team-captain {
          display: flex;
          align-items: center;
          
          .captain-label {
            font-size: 24rpx;
            color: #666;
          }
          
          .captain-name {
            font-size: 24rpx;
            color: #333;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 0;
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
      margin: 20rpx 0 10rpx;
    }
    
    .empty-hint {
      font-size: 24rpx;
      color: #ccc;
    }
  }
  
  .progress-tips {
    .tip-item {
      display: flex;
      align-items: center;
      padding: 12rpx 0;
      
      text {
        margin-left: 10rpx;
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}
</style> 