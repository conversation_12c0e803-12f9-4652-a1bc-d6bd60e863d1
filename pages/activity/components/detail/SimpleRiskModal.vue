<template>
  <view v-if="show" class="modal-overlay" @click.self="handleOverlayClick">
    <view class="modal-container">
      <view class="modal-header">
        <!-- 标题可以从 data prop 获取或保持静态 -->
        {{ modalTitle }} 
        <view class="close-icon" @click="closeModal">&times;</view>
      </view>
      <scroll-view class="modal-content" scroll-y>
        <!-- 使用 s-richtext-block 获取并显示内容 -->
        <s-richtext-block 
          v-if="props.data?.title || props.data?.id" 
          :data="props.data" 
          :styles="richTextStyles" 
        />
        <view v-else class="loading-placeholder">加载中...</view> 
      </scroll-view>
      <view class="modal-footer">
        <button class="confirm-btn" @click="confirmAgreement">我已阅读并同意</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, reactive, computed } from 'vue';
// 导入 s-richtext-block 组件
import SRichtextBlock from '@/sheep/components/s-richtext-block/s-richtext-block.vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  // 修改：接收 data 对象 (包含 id 或 title)
  data: { 
    type: Object,
    default: () => ({ id: undefined, title: '风险协议书' }) // 默认值
  }
});

// 定义 s-richtext-block 的样式 (可以根据需要调整)
const richTextStyles = reactive({
  padding: 20, // 内边距
  // 可以添加其他 s-richtext-block 支持的样式
});

// 从 props.data 获取标题，如果 props.data.title 不存在，则使用默认值
const modalTitle = computed(() => props.data?.title || '风险协议书');

const emits = defineEmits(['confirm', 'close']);

const confirmAgreement = () => {
  // 移除旧的日志
  emits('confirm', true);
};

const closeModal = () => {
  // 移除旧的日志
  emits('close');
};

const handleOverlayClick = () => {
   // 移除旧的日志
   // emits('close');
};

</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* 确保在顶层 */
}

.modal-container {
  background-color: #fff;
  border-radius: 16rpx;
  width: 85%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出圆角 */
}

.modal-header {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  position: relative;
  border-bottom: 1px solid #eee;
}

.close-icon {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-content {
  max-height: 60vh;
  padding: 0; /* 移除外层 padding，交给 s-richtext-block 处理 */
  background-color: #f7f7f7;

  .loading-placeholder {
    padding: 40rpx;
    text-align: center;
    color: #999;
  }
}

.modal-footer {
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx;
  border: none; /* 移除按钮默认边框 */
  padding: 0; /* 移除按钮默认内边距 */
  /* 确保按钮样式与之前的 risk-notification 相似 */
  &::after {
     border: none; /* 移除 uni-app 按钮伪元素边框 */
  }
}
</style> 