<template>
  <view class="signup-progress">
    <!-- 标题栏 -->
    <view class="header" @click="toggleExpand">
      <view class="header-left">
        <uni-icons type="staff-filled" size="20" color="#4a90e2"></uni-icons>
        <view class="title">
          <text>已报名球员 </text>
          <text class="count-parenthesis">(</text>
          <text :class="{'count-overlimit': currentPlayers > maxPlayers}">{{ currentPlayers }}</text>
          <text class="count-parenthesis">/</text>
          <text class="count-parenthesis">{{ maxPlayers }}</text>
          <text class="count-parenthesis">)</text>
        </view>
      </view>
      <view class="header-right">
        <view class="toggle-btn">
          <text>{{ isExpanded ? '收起' : '展开' }}</text>
          <uni-icons 
            type="arrowdown" 
            size="16" 
            color="#999"
            :class="{'arrow-expanded': isExpanded}"
          ></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 头像列表区域 - 一直显示，但根据展开状态调整内容重点 -->
    <view class="avatar-list-container" :class="{'expanded-mode': isExpanded}">
      <!-- 动态标题 -->
      <view class="avatar-section-title" v-if="isExpanded && waitlistPlayers.length > 0">
        <uni-icons type="clock" size="16" color="#FFC107"></uni-icons>
        <text>候补队列 ({{ waitlistPlayers.length }}人等待)</text>
      </view>
      
      <scroll-view scroll-x="true" class="avatar-scroll">
        <!-- 候补球员 -->
        <view v-if="waitlistPlayers && waitlistPlayers.length > 0" class="player-group waitlist-players">
          <view class="avatar-wrapper" v-for="(player, index) in waitlistPlayers" :key="'waitlist-'+player.id">
            <view class="waitlist-badge">候补</view>
            <image class="avatar waitlist" :src="player.avatar || '/static/default-avatar.jpg'" mode="aspectFill"></image>
            <text class="waitlist-number">{{ index+1 }}</text>
          </view>
          
          <!-- 分隔线 - 只在未展开且有确认球员时显示 -->
          <view class="divider" v-if="!isExpanded && confirmedPlayers.length > 0"></view>
        </view>
        
        <!-- 已确认球员预览 - 只在未展开时显示 -->
        <view v-if="!isExpanded && confirmedPlayers.length > 0" class="player-group confirmed-players">
          <view class="avatar-wrapper" v-for="player in confirmedPlayers.slice(0, 8)" :key="'conf-'+player.id">
            <image class="avatar confirmed" :src="player.avatar || '/static/default-avatar.jpg'" mode="aspectFill"></image>
          </view>
          <!-- 更多球员提示 -->
          <view class="more-players" v-if="confirmedPlayers.length > 8">
            <text>+{{ confirmedPlayers.length - 8 }}</text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="!waitlistPlayers.length && !confirmedPlayers.length">
          <text>暂无球员报名</text>
        </view>
        
        <!-- 展开时的候补空状态 -->
        <view class="empty-waitlist" v-if="isExpanded && !waitlistPlayers.length">
          <text>暂无候补球员</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 展开后的队伍内容区域 -->
    <view class="team-content" v-if="isExpanded">
      <view class="team-content-inner">
        <!-- 队伍切换Tab -->
        <view class="team-tabs">
          <view 
            class="team-tab"
            :class="{'active': activeTeam === 'home'}" 
            :style="{'--active-color': homeTeamColor || '#ff4d4f'}"
            @click="switchTeam('home')"
          >
            {{ homeTeamName || '主队' }} ({{ homeTeamPlayers.length }}/{{ teamMaxPlayers }})
          </view>
          <view 
            class="team-tab"
            :class="{'active': activeTeam === 'away'}" 
            :style="{'--active-color': awayTeamColor || '#34c759'}"
            @click="switchTeam('away')"
          >
            {{ awayTeamName || '客队' }} ({{ awayTeamPlayers.length }}/{{ teamMaxPlayers }})
          </view>
        </view>
        
        <!-- 队员列表 -->
        <view class="players-list">
          <!-- 主队列表 -->
          <view v-if="activeTeam === 'home'" class="team-players">
            <view 
              class="player-item" 
              v-for="(player, index) in homeTeamPlayers" 
              :key="'home-player-'+player.id"
              @click="navigateToPlayerCareer(player)"
            >
              <view class="player-index">{{ index + 1 }}</view>
              <image class="player-avatar" :src="player.avatar || '/static/default-avatar.jpg'" mode="aspectFill"></image>
              <view class="player-info">
                <view class="player-name">{{ player.nickname || '球员名称' }}</view>
                <view class="player-position">{{ getPlayerPosition(player.position) }}</view>
              </view>
              <view class="player-score" :style="{color: homeTeamColor || '#ff4d4f'}">{{ formatPlayerScore(player.score) }}</view>
            </view>
            <view class="empty-team" v-if="homeTeamPlayers.length === 0">
              <text>{{ homeTeamName || '主队' }}暂无球员</text>
            </view>
          </view>
          
          <!-- 客队列表 -->
          <view v-else class="team-players">
            <view 
              class="player-item" 
              v-for="(player, index) in awayTeamPlayers" 
              :key="'away-player-'+player.id"
              @click="navigateToPlayerCareer(player)"
            >
              <view class="player-index">{{ index + 1 }}</view>
              <image class="player-avatar" :src="player.avatar || '/static/default-avatar.jpg'" mode="aspectFill"></image>
              <view class="player-info">
                <view class="player-name">{{ player.nickname || '球员名称' }}</view>
                <view class="player-position">{{ getPlayerPosition(player.position) }}</view>
              </view>
              <view class="player-score" :style="{color: awayTeamColor || '#34c759'}">{{ formatPlayerScore(player.score) }}</view>
            </view>
            <view class="empty-team" v-if="awayTeamPlayers.length === 0">
              <text>{{ awayTeamName || '客队' }}暂无球员</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getPositionName } from '@/sheep/store/activity';
import $router from '@/sheep/router'; // 引入路由

// 定义组件属性
const props = defineProps({
  currentPlayers: {
    type: Number,
    default: 0
  },
  maxPlayers: {
    type: Number,
    default: 20
  },
  waitlistPlayers: {
    type: Array,
    default: () => []
  },
  homeTeamPlayers: {
    type: Array,
    default: () => []
  },
  awayTeamPlayers: {
    type: Array,
    default: () => []
  },
  homeTeamName: {
    type: String,
    default: '主队'
  },
  awayTeamName: {
    type: String,
    default: '客队'
  },
  homeTeamColor: {
    type: String,
    default: '#ff4d4f'
  },
  awayTeamColor: {
    type: String,
    default: '#34c759'
  }
});

// 组件内部状态
const isExpanded = ref(false);
const activeTeam = ref('home');

// 计算属性
const confirmedPlayers = computed(() => {
  // 合并两队球员并去重
  const allPlayers = [...props.homeTeamPlayers, ...props.awayTeamPlayers];
  const uniquePlayerIds = new Set();
  return allPlayers.filter(player => {
    if (!player.id || uniquePlayerIds.has(player.id)) {
      return false;
    }
    uniquePlayerIds.add(player.id);
    return true;
  });
});

// 每队最大人数
const teamMaxPlayers = computed(() => {
  return Math.ceil(props.maxPlayers / 2);
});

// 方法
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const switchTeam = (team) => {
  activeTeam.value = team;
};

// 根据枚举获取位置名称
const getPlayerPosition = (position) => {
  // 如果position是数字，则使用枚举函数获取名称
  if (typeof position === 'number') {
    return getPositionName(position);
  }
  // 否则直接返回文本
  return position || '位置';
};

// 导航到球员生涯页面
const navigateToPlayerCareer = (player) => {
  if (!player || !player.id) { // 假设player.id是球员生涯页需要的ID
    uni.showToast({
      title: '无法查看球员详情',
      icon: 'none',
    });
    return;
  }
  
  // 目标页面路径，假设与player-list.vue一致
  const url = `/pages/player/player-career-other?playerId=${player.id}`;
  
  $router.go(url);
};

// 格式化球员能力值
const formatPlayerScore = (score) => {
  return (score / 100).toFixed(2);
};
</script>

<style lang="scss" scoped>
// 颜色变量
$primary-color: #4a90e2;
$warning-color: #FFC107;
$danger-color: #ff4d4f;
$success-color: #34c759;
$border-color: #f0f0f0;
$bg-color: #ffffff;
$text-color: #333333;
$text-secondary: #999999;

// 主容器
.signup-progress {
  background-color: $bg-color;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  
  // 标题栏
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    cursor: pointer;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .title {
        margin-left: 12rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: $text-color;
        display: flex;
        align-items: baseline;
        
        .count-parenthesis {
          color: $text-color;
        }
        
        .count-overlimit {
          color: $danger-color;
          font-weight: bold;
        }
      }
    }
    
    .header-right {
      .toggle-btn {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        background-color: #f5f7fa;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #999;
        
        .arrow-expanded {
          transform: rotate(180deg);
          transition: transform 0.3s;
        }
      }
    }
  }
  
  // 头像列表容器
  .avatar-list-container {
    padding: 10rpx 30rpx 24rpx;
    border-bottom: 1px solid $border-color;
    transition: all 0.3s ease;
    
    // 展开模式下的样式调整
    &.expanded-mode {
      background-color: #fffbf0;
      padding: 16rpx 30rpx 20rpx;
      border-radius: 12rpx;
      margin: 0 20rpx 16rpx;
      border: 1px solid rgba(255, 193, 7, 0.2);
    }
    
    // 动态标题样式
    .avatar-section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #E65100;
      font-weight: 500;
      
      text {
        margin-left: 8rpx;
      }
    }
  }
  
  // 滚动区域
  .avatar-scroll {
    white-space: nowrap;
    &::-webkit-scrollbar {
      display: none;
    }
    
    // 候补组
    .waitlist-players {
      display: inline-flex;
      vertical-align: middle;
      padding: 15rpx 10rpx 5rpx 10rpx;
      margin-right: 10rpx;
      background-color: rgba(255, 193, 7, 0.05);
      border-radius: 30rpx;
    }
    
    // 分隔线
    .divider {
      display: inline-block;
      vertical-align: middle;
      height: 50rpx;
      width: 1px;
      background-color: #e0e0e0;
      margin: 0 15rpx;
    }
    
    // 已确认组
    .confirmed-players {
      display: inline-flex;
      vertical-align: middle;
    }
    
    // 头像包装
    .avatar-wrapper {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      margin: 0 5rpx;
      
      .waitlist-badge {
        position: absolute;
        top: -15rpx;
        left: 50%;
        transform: translateX(-75%);
        background-color: $warning-color;
        color: white;
        font-size: 20rpx;
        padding: 2rpx 12rpx;
        border-radius: 16rpx;
        z-index: 1;
        white-space: nowrap;
      }
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
        border: 2rpx solid transparent;
        
        &.waitlist {
          border-color: $warning-color;
        }
        
        &.confirmed {
          border-color: $primary-color;
        }
      }
      
      .waitlist-number {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 28rpx;
        height: 28rpx;
        border-radius: 50%;
        background-color: $warning-color;
        color: white;
        font-size: 18rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid white;
      }
    }
    
    // 更多球员提示
    .more-players {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #f0f0f0;
      border: 2rpx solid #e0e0e0;
      margin: 0 5rpx;
      vertical-align: middle;
      
      text {
        font-size: 22rpx;
        color: $text-secondary;
        font-weight: 500;
      }
    }
    
    // 空状态
    .empty-state {
      padding: 20rpx 0;
      text-align: center;
      color: $text-secondary;
      font-size: 26rpx;
    }
    
    // 展开时的候补空状态
    .empty-waitlist {
      padding: 24rpx 0;
      text-align: center;
      color: #FF8F00;
      font-size: 26rpx;
      background-color: rgba(255, 193, 7, 0.05);
      border-radius: 8rpx;
      margin: 0 10rpx;
    }
  }
  
  // 队伍内容区域
  .team-content {
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .team-content-inner {
    padding: 20rpx 30rpx 24rpx;
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.1s forwards;
  }

  @keyframes slideDown {
    from {
      max-height: 0;
      opacity: 0;
    }
    to {
      max-height: 1000rpx;
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(-20rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  // 队伍Tab
  .team-tabs {
    display: flex;
    gap: 20rpx;
    margin-bottom: 25rpx;
    
    .team-tab {
      flex: 1;
      text-align: center;
      padding: 18rpx 0;
      border-radius: 8rpx;
      background-color: #f5f5f5;
      font-size: 28rpx;
      color: #666;
      transition: all 0.3s;
      
      &.active {
        color: white;
        font-weight: 500;
        background-color: var(--active-color);
      }
    }
  }
  
  // 队员列表
  .players-list {
    .player-item {
      display: flex;
      align-items: center;
      padding: 25rpx 10rpx;
      border-bottom: 1px solid $border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .player-index {
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
        background-color: #f0f2f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #666;
        font-weight: 500;
        flex-shrink: 0;
      }
      
      .player-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 25rpx;
        background-color: #f5f5f5;
      }
      
      .player-info {
        flex: 1;
        min-width: 0;
        
        .player-name {
          font-size: 30rpx;
          font-weight: 500;
          color: $text-color;
          margin-bottom: 6rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .player-position {
          font-size: 24rpx;
          color: $text-secondary;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .player-score {
        font-size: 32rpx;
        font-weight: 600;
        margin-left: 20rpx;
      }
    }
    
    .empty-team {
      padding: 60rpx 0;
      text-align: center;
      color: $text-secondary;
      font-size: 28rpx;
    }
  }
}
</style> 