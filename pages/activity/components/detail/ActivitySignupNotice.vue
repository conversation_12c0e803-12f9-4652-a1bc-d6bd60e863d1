<template>
  <InfoSection title="报名须知" icon="info" :collapsible="false">
    <view class="notice-list-container">
      <!-- Display noticeText -->
      <view v-if="noticeText" class="notice-block">
        <view class="notice-block-title">注意事项：</view>
        <view class="notice-block-content">{{ noticeText }}</view>
      </view>
      
      <!-- Display rulesText -->
      <view v-if="rulesText" class="notice-block rules-block">
        <view class="notice-block-title">比赛规则：</view>
        <view class="notice-block-content">{{ rulesText }}</view>
      </view>

      <!-- Fallback to static content if props are empty -->
      <template v-if="!noticeText && !rulesText">
        <view class="notice-item">
          <view class="notice-icon">
             <uni-icons type="gear-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="notice-content">
            <view class="notice-title">比赛规则</view>
            <view class="notice-desc">采用FIBA国际篮球标准规则，24秒进攻限制，4节比赛，每节25分钟，整场比赛100分钟，球队需要至少8名球员，最多10名球员</view>
          </view>
        </view>
        <view class="notice-item">
          <view class="notice-icon">
            <uni-icons type="auth-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="notice-content">
            <view class="notice-title">参赛要求</view>
            <view class="notice-desc">参赛球员需身体健康，具备良好的运动体能及一定的篮球技巧，球员需如实填写年龄、姓名及号码，统一球服（颜色、款式），自愿参加比赛并承担相应风险</view>
          </view>
        </view>
        <view class="notice-item">
          <view class="notice-icon">
            <uni-icons type="help-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="notice-content">
            <view class="notice-title">退赛规则</view>
            <view class="notice-desc">停止组局前可随时退赛，费用将原路退回。停止组局后，距比赛开始剩余时间大于2小时，可申请退赛，扣除18%的违约管理费；距比赛开始剩余时间不足2小时，不可申请退赛。</view>
          </view>
        </view>
        <view class="notice-item">
          <view class="notice-icon">
            <uni-icons type="medal-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="notice-content">
            <view class="notice-title">赛制说明</view>
            <view class="notice-desc">采用双循环赛制，每支球队至少打满10场比赛，前4名进入季后赛，半决赛和决赛采用三场两胜制</view>
          </view>
        </view>
        <view class="notice-item">
          <view class="notice-icon">
             <uni-icons type="settings-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="notice-content">
            <view class="notice-title">分队规则</view>
            <view class="notice-desc">系统采用多维度均衡分配机制，确保两队实力均衡。好友组队模式下，系统在确保两队实力均衡的前提下，优先将好友分配至同一队伍，无法确保两队实力均衡时，可能拆分好友组队以满足公平性。</view>
          </view>
        </view>
      </template>
    </view>
  </InfoSection>
</template>

<script setup>
import InfoSection from './InfoSection.vue';

// Define props if needed in the future, e.g., to pass notice details dynamically
const props = defineProps({
  noticeText: {
    type: String,
    default: ''
  },
  rulesText: {
    type: String,
    default: ''
  }
  // notice: {
  //   type: Array,
  //   default: () => []
  // }
});
</script>

<style lang="scss" scoped>
.notice-block {
  margin-bottom: 30rpx;
  .notice-block-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 10rpx;
  }
  .notice-block-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    white-space: pre-line; // Preserve line breaks from the text
  }
  &.rules-block {
    margin-top: 20rpx; // Add some space if both are present
  }
}

/* Styles moved from detail.vue */
.notice-list-container {
   padding: 15rpx 0;
}
.notice-item {
  display: flex;
  gap: 24rpx;
  margin-bottom: 40rpx;
  &:last-child {
    margin-bottom: 0;
  }
}
.notice-icon {
   width: 72rpx;
   height: 72rpx;
   border-radius: 50%;
   background: rgba(74, 144, 226, 0.1);
   display: flex;
   align-items: center;
   justify-content: center;
   flex-shrink: 0;
}
.notice-content {
  flex: 1;
}
.notice-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8rpx;
}
.notice-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.5;
}
</style> 