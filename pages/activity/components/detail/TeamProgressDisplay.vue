<template>
  <view class="team-progress-display">
    <!-- 标题栏: 显示已报名球队数量 -->
    <view class="header" @click="toggleExpand">
      <view class="header-left">
        <uni-icons type="flag-filled" size="20" color="#4a90e2"></uni-icons>
        <view class="title">
          <text>{{ title }} </text>
          <text class="count-parenthesis">({{ currentTeams }}/{{ maxTeams }})</text>
        </view>
      </view>
      <view class="header-right">
        <view class="toggle-btn">
          <text>{{ isExpanded ? '收起' : '展开' }}</text>
          <uni-icons 
            type="arrowdown" 
            size="16" 
            color="#999"
            :class="{'arrow-expanded': isExpanded}"
          ></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 球队Logo列表区域 - 通过CSS控制显隐 -->
    <view 
      class="team-logo-list-container logo-collapsible-content" 
      :class="{ 'expanded': !isExpanded }" 
    >
      <scroll-view scroll-x="true" class="team-logo-scroll">
        <view class="team-logo-wrapper" v-for="team in signedUpTeams" :key="'team-logo-'+team.id">
          <image 
            class="team-logo" 
            :src="getTeamLogo(team)" 
            mode="aspectFill"
            @error="handleImageError"
          />
        </view>
        <!-- 空状态 -->
        <view class="empty-state" v-if="!signedUpTeams || signedUpTeams.length === 0">
          <text>暂无球队报名</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 展开后的队伍详情区域 -->
    <view 
      class="team-details-content collapsible-content" 
      :class="{ expanded: isExpanded }"
    >
      <!-- 达到条件的球队 -->
      <view v-if="qualifiedTeams.length > 0" class="team-group">
        <view class="group-header qualified">
          <uni-icons type="checkmarkempty" size="18" color="#52c41a"></uni-icons>
          <text class="group-title">报名成功 ({{ qualifiedTeams.length }}支队伍)</text>
        </view>
        <view class="team-block" v-for="team in qualifiedTeams" :key="'qualified-'+team.id">
          <TeamCard 
            :team="team" 
            :minPlayers="minPlayersPerTeam"
            :maxPlayersPerTeam="maxPlayersPerTeam"
            :isQualified="true"
            @team-click="handleTeamClick"
            @player-click="handlePlayerClick"
          />
        </view>
      </view>

      <!-- 未达到条件的球队 -->
      <view v-if="unqualifiedTeams.length > 0" class="team-group">
        <view class="group-header unqualified">
          <uni-icons type="info" size="18" color="#ff9800"></uni-icons>
          <text class="group-title">以下球队人数不足</text>
        </view>
        <view class="team-block" v-for="team in unqualifiedTeams" :key="'unqualified-'+team.id">
          <TeamCard 
            :team="team" 
            :minPlayers="minPlayersPerTeam"
            :maxPlayersPerTeam="maxPlayersPerTeam"
            :isQualified="false"
            @team-click="handleTeamClick"
            @player-click="handlePlayerClick"
          />
        </view>
      </view>

      <!-- 全部队伍都为空的状态 -->
      <view v-if="signedUpTeams.length === 0" class="empty-all-teams">
        <uni-icons type="people" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">暂无队伍报名</text>
        <text class="empty-hint">成为第一支报名的队伍吧！</text>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import $router from '@/sheep/router';
import sheep from '@/sheep';
import TeamCard from './TeamCard.vue';

// 定义组件属性
const props = defineProps({
  title: {
    type: String,
    default: '已报名球队'
  },
  currentTeams: {
    type: Number,
    default: 0
  },
  maxTeams: {
    type: Number,
    default: 2
  },
  signedUpTeams: {
    type: Array,
    default: () => []
  },
  minPlayersPerTeam: {
    type: Number,
    default: 5 // 默认最小人数
  },
  maxPlayersPerTeam: {
    type: Number,
    default: 10 // 默认最大人数
  }
});

// 组件内部状态
const isExpanded = ref(false);

// 计算属性：达到条件的球队
const qualifiedTeams = computed(() => {
  return props.signedUpTeams.filter(team => {
    const playerCount = getPlayerCount(team);
    return playerCount >= props.minPlayersPerTeam;
  });
});

// 计算属性：未达到条件的球队
const unqualifiedTeams = computed(() => {
  return props.signedUpTeams.filter(team => {
    const playerCount = getPlayerCount(team);
    return playerCount < props.minPlayersPerTeam;
  });
});

// 方法
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 获取球队logo，兼容不同的数据格式
const getTeamLogo = (team) => {
  // 优先使用 logoUrl，然后是 logo，最后是默认图片
  const logoUrl = team.logoUrl || team.logo;
  if (logoUrl) {
    // 如果是相对路径，使用CDN处理
    return logoUrl.startsWith('http') ? logoUrl : sheep.$url.cdn(logoUrl);
  }
  return '/static/images/default-team-logo.png';
};

// 获取球员数量，兼容不同的数据格式
const getPlayerCount = (team) => {
  if (team.memberCount !== undefined) {
    return team.memberCount; // 联赛格式
  }
  return team.players?.length || 0; // 友谊赛格式
};

// 图片加载错误处理
const handleImageError = (e) => {
  console.warn('队伍logo加载失败:', e);
  // 可以设置默认图片
  e.target.src = '/static/images/default-team-logo.png';
};


// 事件处理
const handleTeamClick = (team) => {
  const teamId = team.teamId || team.id;
  if (!teamId) {
    uni.showToast({
      title: '无法查看球队详情',
      icon: 'none',
    });
    return;
  }
  const url = `/pages/team/team-info?id=${teamId}`;
  $router.go(url);
};

const handlePlayerClick = (player) => {
  if (!player || !player.id) { 
    uni.showToast({
      title: '无法查看球员详情',
      icon: 'none',
    });
    return;
  }
  const url = `/pages/player/player-career-other?playerId=${player.id}`;
  $router.go(url);
};

</script>

<style lang="scss" scoped>
// 基础颜色变量
$primary-color: #4a90e2;
$success-color: #52c41a;
$warning-color: #ff9800;
$border-color: #f0f0f0;
$bg-color: #ffffff;
$text-color: #333333;
$text-secondary: #999999;

// 主容器
.team-progress-display {
  background-color: $bg-color;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

// 标题栏
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  cursor: pointer;
  border-bottom: 1px solid $border-color;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12rpx;
    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: $text-color;
      .count-parenthesis {
        color: $text-color;
        margin-left: 4rpx;
      }
    }
  }

  .header-right {
    .toggle-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background-color: #f5f7fa;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #999;
      gap: 4rpx;
      .arrow-expanded {
        transform: rotate(180deg);
        transition: transform 0.3s;
      }
    }
  }
}

// 球队Logo列表容器
.team-logo-list-container {
  max-height: 120rpx;
  opacity: 1;
  overflow: hidden;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out, padding 0.4s ease-out;
  padding: 20rpx 30rpx;
  box-sizing: border-box;

  &.logo-collapsible-content {
    &:not(.expanded) {
      max-height: 0;
      opacity: 0;
      padding-top: 0;
      padding-bottom: 0;
      border-bottom-color: transparent;
    }
    &.expanded {
      max-height: 120rpx;
      opacity: 1;
      padding-top: 20rpx;
      padding-bottom: 20rpx;
      border-bottom-color: $border-color;
    }
  }
}

// Logo滚动区域
.team-logo-scroll {
  white-space: nowrap;
  &::-webkit-scrollbar {
    display: none;
  }

  .team-logo-wrapper {
    display: inline-block;
    margin-right: 20rpx;
    &:last-child {
      margin-right: 0;
    }
  }
  
  .team-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    border: 1px solid $border-color;
  }

  .empty-state {
    padding: 20rpx 0;
    text-align: left;
    color: $text-secondary;
    font-size: 26rpx;
  }
}

// 展开后的队伍详情区域
.team-details-content {
  padding: 0 30rpx 24rpx;
}

// 可折叠内容容器样式
.collapsible-content {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out, padding 0.4s ease-out;
  padding-top: 0;
  padding-bottom: 0;
  box-sizing: border-box;
}

.collapsible-content.expanded {
  max-height: 2000px; // 足够大的高度
  opacity: 1;
  padding-top: 20rpx;
  padding-bottom: 24rpx;
}

// 队伍分组
.team-group {
  margin-bottom: 30rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  
  &.qualified {
    background-color: #f6ffed;
    border: 1px solid #d9f7be;
    
    .group-title {
      color: $success-color;
      font-weight: 600;
    }
  }
  
  &.unqualified {
    background-color: #fff7e6;
    
    .group-title {
      color: $warning-color;
    }
  }
  
  .group-title {
    font-size: 28rpx;
  }
}

// 分割线
.divider {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
  
  .divider-line {
    flex: 1;
    height: 1px;
    background-color: #e8e8e8;
  }
  
  .divider-text {
    padding: 0 20rpx;
    font-size: 24rpx;
    color: $text-secondary;
    background-color: $bg-color;
  }
}

.team-block {
  margin-bottom: 20rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

// 空状态
.empty-all-teams {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin: 20rpx 0 10rpx;
  }
  
  .empty-hint {
    font-size: 24rpx;
    color: #ccc;
  }
}
</style> 