<template>
  <view class="info-section">
    <view class="section-header" @click="toggleCollapse" v-if="title">
      <view class="header-left">
        <view class="section-icon" v-if="icon">
          <uni-icons :type="icon" size="20" color="#4080FF"></uni-icons>
        </view>
        <text class="section-title">{{ title }}</text>
      </view>
      <view class="header-right">
        <slot name="header-extra"></slot>
        <view v-if="collapsible" class="expand-button">
          <text>{{ isExpanded ? '收起' : '展开' }}</text>
          <uni-icons 
            type="arrowdown" 
            size="16" 
            color="#7f8c8d" 
            class="arrow-icon" 
            :class="{ 'expanded': isExpanded }"
          ></uni-icons>
        </view>
      </view>
    </view>
    <view class="section-content-wrapper" :style="{ maxHeight: contentMaxHeight }">
      <view class="section-content">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  collapsible: {
    type: Boolean,
    default: false
  },
  initiallyExpanded: {
    type: Boolean,
    default: true
  },
});

const isExpanded = ref(props.initiallyExpanded);

const toggleCollapse = () => {
  if (props.collapsible) {
    isExpanded.value = !isExpanded.value;
  }
};

const contentMaxHeight = computed(() => {
  return isExpanded.value ? '1000px' : '0';
});
</script>

<style lang="scss" scoped>
.info-section {
  margin-bottom: 20rpx;
  background-color: transparent;
  border-radius: 12rpx;
  overflow: hidden;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    min-height: 88rpx;
    box-sizing: border-box;
    cursor: pointer;
    background-color: #fff;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .section-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
      }
      
      .section-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .expand-button {
        display: flex;
        align-items: center;
        gap: 4rpx;
        color: #7f8c8d;
        font-size: 26rpx;
        cursor: pointer;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        background: #f5f7fa;
        margin-left: auto;

        .arrow-icon {
          transition: transform 0.3s ease;
        }

        .arrow-icon.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }
  
  .section-content-wrapper {
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    background-color: #fff;
  }
  
  .section-content {
    padding: 0 30rpx 24rpx 30rpx;
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    border-top: 1px solid #f0f0f0;
  }
}
</style> 