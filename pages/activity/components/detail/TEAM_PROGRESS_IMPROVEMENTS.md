# 队伍进度展示组件改进说明

## 问题解决

### 1. 球队头像显示问题
**问题**：报名完成的球队，球队头像没有正确展示
**解决方案**：
- 统一了球队logo字段的处理逻辑，兼容 `logoUrl`（友谊赛格式）和 `logo`（联赛格式）
- 添加了 CDN 路径处理和错误兜底逻辑
- 提供了默认图片 `/static/images/default-team-logo.png` 作为 fallback

```javascript
// 统一的logo获取逻辑
const getTeamLogo = (team) => {
  const logoUrl = team.logoUrl || team.logo;
  if (logoUrl) {
    return logoUrl.startsWith('http') ? logoUrl : sheep.$url.cdn(logoUrl);
  }
  return '/static/images/default-team-logo.png';
};
```

### 2. 组件复用性问题
**问题**：联赛和友谊赛的参赛队伍情况展示代码重复
**解决方案**：
- 创建了统一的 `TeamProgressDisplay.vue` 组件
- 兼容不同的数据格式（友谊赛的 `players` 数组和联赛的 `memberCount` 字段）
- 替换了原有的 `FriendlyMatchSignupProgress.vue` 和 `LeagueTeamProgress.vue`

### 3. 队伍分组展示功能
**问题**：希望把达到最小人数限制的球队和没达到条件的球队分开展示
**解决方案**：
- 实现了队伍分组逻辑，根据 `minPlayersPerTeam` 参数自动分组
- 达到条件的队伍显示在上方，带绿色标识
- 未达到条件的队伍显示在下方，带橙色标识
- 中间使用分割线清晰区分

## 新组件结构

### TeamProgressDisplay.vue（统一组件）
- **功能**：展示队伍报名进度，支持分组显示
- **特点**：
  - 兼容友谊赛和联赛的不同数据格式
  - 自动分组显示合格/不合格队伍
  - 统一的图片加载错误处理
  - 可展开/收起的界面设计

### TeamCard.vue（队伍卡片）
- **功能**：展示单个队伍的详细信息
- **特点**：
  - 根据合格状态显示不同的视觉样式
  - 兼容友谊赛（球员列表）和联赛（队长信息）格式
  - 支持点击跳转到队伍和球员详情页

## 使用方式

```vue
<!-- 在 ActivityProgressSection.vue 中 -->
<TeamProgressDisplay 
  v-if="activityType === ACTIVITY_TYPE.FRIENDLY_MATCH"
  title="已报名球队"
  :currentTeams="currentTeams"
  :maxTeams="maxTeams" 
  :signedUpTeams="signedUpTeams"
  :minPlayersPerTeam="minPlayersPerTeam || 5"
/>
```

## 数据格式兼容

### 友谊赛格式
```javascript
{
  id: 1,
  name: "球队名称",
  logoUrl: "team_logo.png",
  color: "#ff4d4f",
  players: [
    { id: 1, avatar: "avatar.jpg", nickname: "球员1" }
  ],
  maxPlayers: 15
}
```

### 联赛格式
```javascript
{
  teamId: 1,
  name: "球队名称", 
  logo: "team_logo.png",
  memberCount: 8,
  captain: { name: "队长名称" }
}
```

## 视觉效果

### 分组展示
- ✅ **报名成功**：绿色背景，对勾图标
- ⚠️ **人数不足**：橙色背景，感叹号图标
- 中间分割线清晰区分两个分组

### 队伍卡片
- 合格队伍：绿色边框和阴影
- 不合格队伍：橙色边框和阴影
- 显示"还需X人"的提示信息

## 文件更改清单

### 新增文件
- `TeamProgressDisplay.vue` - 统一队伍进度组件
- `TeamCard.vue` - 队伍卡片组件

### 修改文件
- `ActivityProgressSection.vue` - 使用新的统一组件
- `detail.vue` - 添加 `minPlayersPerTeam` 参数传递

### 可移除文件（可选）
- `FriendlyMatchSignupProgress.vue` - 已被统一组件替代
- `LeagueTeamProgress.vue` - 已被统一组件替代

## 注意事项

1. **图片资源**：确保默认图片文件存在
   - `/static/images/default-team-logo.png`
   - `/static/images/default-avatar.jpg`

2. **数据兼容性**：新组件能处理现有的数据格式，无需后端改动

3. **性能优化**：使用了图片懒加载和错误处理，提升用户体验

4. **可访问性**：添加了合适的图标和颜色区分，便于用户快速识别队伍状态 