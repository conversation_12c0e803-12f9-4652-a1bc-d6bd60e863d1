<template>
  <InfoSection title="服务明细" icon="list" :collapsible="true" :initiallyExpanded="false">
    <view class="service-list-container">
      <!-- If servicesText is a simple string, display it directly -->
      <view v-if="servicesText && typeof servicesText === 'string'" class="service-text-block">
        {{ servicesText }}
      </view>
      <!-- Else, if it's an array or needs specific parsing, you might need more complex rendering -->
      <!-- For now, keeping the static content as fallback if servicesText is not provided or not a string -->
      <template v-else>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="gear-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">标准FIBA场地</view>
            <view class="service-desc">国际篮联认证标准篮球场地，满足高水平比赛需求</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="person-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">专业裁判团队</view>
            <view class="service-desc">国家认证经验丰富裁判团队，确保比赛公平公正</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="person-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">计分计时员</view>
            <view class="service-desc">专业计分计时团队，负责计分、计时、24秒进攻计时等</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="images-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">比赛专用电子屏</view>
            <view class="service-desc">显示比分、时间、球员犯规等信息的电子记分牌</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="refreshempty" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">24秒计时器</view>
            <view class="service-desc">标准24秒进攻计时设备，位于球场两端</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="videocam-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">AI自动跟拍设备</view>
            <view class="service-desc">智能跟踪拍摄系统，实时捕捉比赛精彩瞬间</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="phone-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">高清录像设备</view>
            <view class="service-desc">采用专业录像设备，4K画质录制全场比赛</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="videocam-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">全场视频回放</view>
            <view class="service-desc">赛后提供完整比赛视频，支持在线观看、下载剪辑等功能</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="star-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">进球集锦</view>
            <view class="service-desc">AI自动剪辑比赛集锦，展示本场比赛精彩瞬间</view>
          </view>
        </view>
        <view class="service-item">
          <view class="service-icon">
            <uni-icons type="tune-filled" size="20" color="#4a90e2"></uni-icons>
          </view>
          <view class="service-info">
            <view class="service-name">数据统计</view>
            <view class="service-desc">AI自动统计结合专业数据团队复核，记录详细个人和球队数据，生成数据报表</view>
          </view>
        </view>
      </template>
    </view>
  </InfoSection>
</template>

<script setup>
import InfoSection from './InfoSection.vue';

// Define props if needed in the future, e.g., to pass service list dynamically
const props = defineProps({
  servicesText: {
    type: String,
    default: ''
  }
  // services: {
  //   type: Array,
  //   default: () => []
  // }
});
</script>

<style lang="scss" scoped>
.service-text-block {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-line;
  padding: 20rpx 0;
}

/* Styles moved from detail.vue */
.service-list-container {
  padding: 15rpx 0;
}
.service-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding: 15rpx 0;
}
.service-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(74, 144, 226, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.service-info {
  flex: 1;
}
.service-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8rpx;
}
.service-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.4;
}
</style> 