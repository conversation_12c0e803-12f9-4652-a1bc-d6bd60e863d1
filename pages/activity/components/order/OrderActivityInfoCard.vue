<template>
  <view class="section activity-info">
    <view class="section-title">
      <uni-icons type="basketball" size="18" color="#4A90E2"></uni-icons>
      <text>赛事信息</text>
    </view>
    <view class="activity-card">
      <view class="activity-header">
        <view class="activity-icon">
          <uni-icons type="flag" size="24" color="#4A90E2"></uni-icons>
          </view>
        <view class="activity-content">
          <view class="activity-title">{{ activity?.title || '香蜜湖赛区 - 排位赛' }}</view>
          </view>
        <view class="activity-type-badge" :class="getTypeBadgeClass(activity?.activityType)">
          {{ getActivityTypeText(activity?.activityType) }}
        </view>
      </view>
      
      <view class="activity-details">
        <view class="detail-row">
          <view class="detail-icon">
            <uni-icons type="calendar" size="16" color="#4A90E2"></uni-icons>
          </view>
          <text class="detail-text">{{ formatDateTime(activity?.startTime) }}</text>
        </view>
        <view class="detail-row">
          <view class="detail-icon">
            <uni-icons type="location" size="16" color="#4A90E2"></uni-icons>
          </view>
          <text class="detail-text">{{ activity?.location || '深圳市南山区香蜜湖体育中心' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { ACTIVITY_TYPE } from '@/sheep/store/activity';

const props = defineProps({
  activity: {
    type: Object,
    default: () => ({})
  }
});

/**
 * 获取活动类型文本
 */
const getActivityTypeText = (type) => {
  const typeMap = {
    [ACTIVITY_TYPE.RANKING_MATCH]: '排位赛',
    [ACTIVITY_TYPE.FRIENDLY_MATCH]: '友谊赛',
    [ACTIVITY_TYPE.LEAGUE]: '联赛'
  };
  return typeMap[type] || '排位赛';
};


/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return '2024.03.27 周四 19:30-21:30';
  
  // 如果已经是格式化的字符串，直接返回
  if (typeof dateTime === 'string' && dateTime.includes('.')) {
    return dateTime;
  }
  
  // 否则格式化时间戳
  try {
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    const weekday = weekdays[date.getDay()];
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}.${month}.${day} 周${weekday} ${hours}:${minutes}-${parseInt(hours)+2}:${minutes}`;
  } catch (error) {
    return '2024.03.27 周四 19:30-21:30';
  }
};

/**
 * 获取类型徽章样式类
 */
const getTypeBadgeClass = (type) => {
  const classMap = {
    [ACTIVITY_TYPE.RANKING_MATCH]: 'type-ranking',
    [ACTIVITY_TYPE.FRIENDLY_MATCH]: 'type-friendly',
    [ACTIVITY_TYPE.LEAGUE]: 'type-league'
  };
  return classMap[type] || 'type-ranking';
};
</script>

<style lang="scss" scoped>
.section {
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .section-title {
  display: flex;
  align-items: center;
    gap: 8rpx;
    font-size: 32rpx;
  font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 24rpx;

    text {
      color: #1a1a1a;
    }
  }
}

.activity-card {
  .activity-header {
  display: flex;
    align-items: flex-start;
  padding: 24rpx;
    background: linear-gradient(135deg, #f8fafe 0%, #f0f8ff 100%);
    border-radius: 12rpx;
    border: 1rpx solid #e8f4ff;
    margin-bottom: 24rpx;

    .activity-icon {
      width: 48rpx;
      height: 48rpx;
      background: linear-gradient(135deg, #4A90E2 0%, #5c6bc0 100%);
      border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
      margin-right: 16rpx;
      box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);

      :deep(.uni-icons) {
        color: #fff !important;
      }
}

    .activity-content {
  flex: 1;
      min-width: 0;

      .activity-title {
        font-size: 32rpx;
  font-weight: 600;
        color: #1a1a1a;
  margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
}

      .activity-subtitle {
  font-size: 26rpx;
        color: #666;
        font-weight: 400;
      }
    }

    .activity-type-badge {
      font-size: 24rpx;
      font-weight: 500;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      white-space: nowrap;
      margin-left: 16rpx;

      &.type-ranking {
        background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
        color: #1890ff;
        border: 1rpx solid #91d5ff;
      }

      &.type-friendly {
        background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        color: #52c41a;
        border: 1rpx solid #b7eb8f;
      }

      &.type-league {
        background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
        color: #f5222d;
        border: 1rpx solid #ffa39e;
      }
    }
}

  .activity-details {
    .detail-row {
  display: flex;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
}

.detail-icon {
        width: 40rpx;
        height: 40rpx;
        background: linear-gradient(135deg, #f8fafe 0%, #e8f4ff 100%);
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;
        border: 1rpx solid #e8f4ff;
}

.detail-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        line-height: 1.4;
  flex: 1;
      }
    }
  }
}
</style> 