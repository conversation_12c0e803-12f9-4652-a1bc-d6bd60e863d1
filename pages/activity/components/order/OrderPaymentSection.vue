<template>
  <view class="section payment-options" v-if="shouldShow">
    <view class="section-header">
      <uni-icons type="credit-card" size="20" color="#4A90E2"></uni-icons>
      <text class="section-title">支付方式选择</text>
    </view>

    <!-- 友谊赛支付方式 -->
    <template v-if="activityType === ACTIVITY_TYPE.FRIENDLY_MATCH">
      <!-- 支付方式已固定的提示 -->
      <view v-if="isTeamPaymentTypeFixed" class="payment-fixed-notice">
        <uni-icons type="info-filled" size="16" color="#4A90E2"></uni-icons>
        <text class="notice-text">球队已确定使用 {{ fixedPaymentTypeText }}，其他队友需使用相同支付方式</text>
      </view>

      <!-- 球队整体支付（推荐） -->
      <view 
        class="payment-option" 
        :class="{ 
          'option-selected': teamPaymentMethod === 'team_total' || (isTeamPaymentTypeFixed && fixedPaymentMethod === 'team_total'), 
          'option-recommended': true,
          'option-disabled': isTeamPaymentTypeFixed && fixedPaymentMethod !== 'team_total'
        }"
        @click="selectPaymentMethod('team_total')"
      >
        <view class="option-content">
          <view class="option-header">
            <view class="option-title-wrapper">
              <text class="option-title">球队整体支付（参赛2队平摊）</text>
              <!-- <view class="recommended-badge">推荐</view> -->
            </view>
            <view class="option-amount">¥{{ teamFeeFixed }}</view>
          </view>
          <view class="option-desc">两队平摊赛事费用，支付后若最终参与人数未满{{ minPlayersConfig }}人或取消报名，全部费用将自动原路退还</view>
        </view>
        <view class="option-selector">
          <view class="radio-button" :class="{ 'radio-selected': teamPaymentMethod === 'team_total' }">
            <uni-icons v-if="teamPaymentMethod === 'team_total'" type="checkmark" size="16" color="#fff"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 球员个人支付（AA制） -->
      <view 
        class="payment-option" 
        :class="{ 
          'option-selected': teamPaymentMethod === 'team_aa' || (isTeamPaymentTypeFixed && fixedPaymentMethod === 'team_aa'),
          'option-disabled': isTeamPaymentTypeFixed && fixedPaymentMethod !== 'team_aa'
        }"
        @click="selectPaymentMethod('team_aa')"
      >
        <view class="option-content">
          <view class="option-header">
            <view class="option-title-wrapper">
              <text class="option-title">球员个人支付（队内{{ minPlayersConfig }}人平摊）</text>
            </view>
            <view class="option-amount">¥{{ aaFeeFixed }}</view>
          </view>
          <view class="option-desc">按最低组局人数计算，支付后若实际参与人数超过{{ minPlayersConfig }}人，多收部分将自动原路退还</view>
        </view>
        <view class="option-selector">
          <view class="radio-button" :class="{ 'radio-selected': teamPaymentMethod === 'team_aa' }">
            <uni-icons v-if="teamPaymentMethod === 'team_aa'" type="checkmark" size="16" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </template>

    <!-- 联赛支付方式 -->
    <template v-else-if="activityType === ACTIVITY_TYPE.LEAGUE">
      <view class="payment-option option-selected">
        <view class="option-content">
          <view class="option-header">
            <view class="option-title-wrapper">
              <text class="option-title">个人报名费</text>
            </view>
            <view class="option-amount">¥{{ (estimatedFeePerPlayer / 100).toFixed(2) }}</view>
          </view>
          <view class="option-desc">每人支付固定报名费</view>
        </view>
        <view class="option-selector">
          <view class="radio-button radio-selected">
            <uni-icons type="checkmark" size="16" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { ACTIVITY_TYPE, SIGNUP_MODE } from '@/sheep/store/activity';

const props = defineProps({
  activityType: {
    type: Number,
    required: true
  },
  signupMode: {
    type: Number,
    required: true
  },
  teamPaymentMethod: {
    type: String,
    default: 'team_total'
  },
  // 基础费用信息（来自活动配置，不受支付方式影响）
  activityFeeInfo: {
    type: Object,
    default: () => ({
      venueFeeAmount: 0,
      serviceFeeAmount: 0,
      minPlayersConfig: 5,
      maxPlayersConfig: 10,
      estimatedFeePerPlayer: 0,
      // 友谊赛固定费用
      friendlyTeamFee: 0,
      friendlyAAFee: 0,
      // 球队已确定的支付方式
      teamPaymentType: null
    })
  },
  estimatedFeePerPlayer: {
    type: Number,
    default: 0
  },
  teamMembersCount: {
    type: Number,
    default: 5
  }
});

const emit = defineEmits(['update:teamPaymentMethod']);

/**
 * 最小人数配置
 */
const minPlayersConfig = computed(() => {
  return props.activityFeeInfo?.minPlayersConfig || 5;
});

/**
 * 计算队伍费用（使用后端返回的固定费用）
 */
const teamFeeFixed = computed(() => {
  if (props.activityFeeInfo?.friendlyTeamFee) {
    return (props.activityFeeInfo.friendlyTeamFee / 100).toFixed(2);
  }
  
  // 兜底逻辑：如果后端没有返回，则本地计算
  const totalFee = (props.activityFeeInfo?.venueFeeAmount || 0) + (props.activityFeeInfo?.serviceFeeAmount || 0);
  const teamFee = Math.ceil(totalFee / 2); // 向上取整
  return (teamFee / 100).toFixed(2);
});

/**
 * 计算AA制费用（使用后端返回的固定费用）
 */
const aaFeeFixed = computed(() => {
  if (props.activityFeeInfo?.friendlyAAFee) {
    return (props.activityFeeInfo.friendlyAAFee / 100).toFixed(2);
  }
  
  // 兜底逻辑：如果后端没有返回，则本地计算
  const totalFee = (props.activityFeeInfo?.venueFeeAmount || 0) + (props.activityFeeInfo?.serviceFeeAmount || 0);
  const teamFee = Math.ceil(totalFee / 2); // 向上取整
  const aaFee = Math.ceil(teamFee / minPlayersConfig.value); // 向上取整
  return (aaFee / 100).toFixed(2);
});

/**
 * 是否显示支付方式选择
 */
const shouldShow = computed(() => {
  // 友谊赛球队报名时显示支付方式选择
  if (props.activityType === ACTIVITY_TYPE.FRIENDLY_MATCH && props.signupMode === SIGNUP_MODE.TEAM) {
    return true;
  }
  
  // 联赛球队报名时显示支付方式（固定个人费用）
  if (props.activityType === ACTIVITY_TYPE.LEAGUE && props.signupMode === SIGNUP_MODE.TEAM) {
    return true;
  }
  
  return false;
});

/**
 * 球队支付方式是否已确定
 */
const isTeamPaymentTypeFixed = computed(() => {
  return props.activityFeeInfo?.teamPaymentType !== null && props.activityFeeInfo?.teamPaymentType !== undefined;
});

/**
 * 已确定的支付方式文本
 */
const fixedPaymentTypeText = computed(() => {
  if (!isTeamPaymentTypeFixed.value) return '';
  
  const teamPaymentType = props.activityFeeInfo.teamPaymentType;
  if (teamPaymentType === 1) {
    return '球队整体支付';
  } else if (teamPaymentType === 2) {
    return '球员个人支付（AA制）';
  }
  return '';
});

/**
 * 根据已确定的支付方式获取对应的method值
 */
const fixedPaymentMethod = computed(() => {
  if (!isTeamPaymentTypeFixed.value) return null;
  
  const teamPaymentType = props.activityFeeInfo.teamPaymentType;
  if (teamPaymentType === 1) {
    return 'team_total';
  } else if (teamPaymentType === 2) {
    return 'team_aa';
  }
  return null;
});

/**
 * 选择支付方式
 */
const selectPaymentMethod = (method) => {
  // 如果支付方式已固定，不允许更改
  if (isTeamPaymentTypeFixed.value) {
    uni.showToast({
      title: `球队已确定使用${fixedPaymentTypeText.value}，无法更改`,
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  emit('update:teamPaymentMethod', method);
};
</script>

<style lang="scss" scoped>
.section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-left: 12rpx;
  }
}

.payment-fixed-notice {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f0f8ff;
  border: 1rpx solid #e1f3ff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  .notice-text {
    font-size: 24rpx;
    color: #4A90E2;
    margin-left: 12rpx;
    line-height: 1.4;
  }
}

.payment-option {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &.option-selected {
    background-color: #f8fafe;
    
    &.option-recommended {
      background: linear-gradient(135deg, #f8fafe 0%, #f0f8ff 100%);
    }
  }

  &.option-disabled {
    opacity: 0.5;
    background-color: #f5f5f5;
    cursor: not-allowed;
    
    .option-content .option-header .option-title {
      color: #999;
    }
    
    .option-content .option-desc {
      color: #ccc;
    }
    
    .option-selector .radio-button {
      border-color: #ddd;
      background-color: #f5f5f5;
    }
  }

  &.option-recommended::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: linear-gradient(180deg, #4A90E2 0%, #5c6bc0 100%);
  }

  .option-content {
    flex: 1;
    
    .option-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 12rpx;
      
      .option-title-wrapper {
        display: flex;
        align-items: center;
        flex: 1;
        
        .option-title {
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          line-height: 1.4;
        }
        
        .recommended-badge {
          background: linear-gradient(135deg, #f59520 0%, #f7b500 100%);
          color: #fff;
          font-size: 20rpx;
          font-weight: 500;
          padding: 6rpx 16rpx;
          border-radius: 24rpx;
          margin-left: 16rpx;
          white-space: nowrap;
          flex-shrink: 0;
          display: inline-block;
          line-height: 1.2;
          text-align: center;
          min-width: 60rpx;
          box-sizing: border-box;
        }
      }
      
      .option-amount {
        font-size: 32rpx;
        font-weight: 600;
        color: #f59520;
        margin-left: 20rpx;
      }
    }

    .option-desc {
      font-size: 24rpx;
      color: #7f8c8d;
      line-height: 1.5;
      margin-right: 60rpx;
    }
  }

  .option-selector {
    margin-left: 20rpx;
    
    .radio-button {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      
      &.radio-selected {
        background: linear-gradient(135deg, #4A90E2 0%, #5c6bc0 100%);
        border-color: #4A90E2;
      }
    }
  }
}
</style> 