<template>
  <view class="player-profile-container">
    <!-- 头部区域 -->
    <view class="profile-header">
      <view class="header-icon-title"> 
        <uni-icons type="person-filled" size="20" color="#495057"></uni-icons> 
        <text class="header-title">创建球员生涯</text> 
      </view>
      <text class="header-desc">完善您的球员信息，记录您的篮球成长轨迹</text>
    </view>

    <!-- 表单内容区域 -->
    <view class="profile-form">
      <!-- 头像上传 -->
      <view class="avatar-section">
        <view class="avatar-container" @tap="chooseAvatar">
          <image v-if="localData.avatar" :src="localData.avatar" mode="aspectFill" class="avatar-image"></image>
          <view v-else class="avatar-placeholder">
            <uni-icons type="camera" size="28" color="#bdbdbd"></uni-icons>
          </view>
        </view>
        <text class="avatar-hint">点击上传头像</text>
      </view>

      <!-- 姓名和号码行 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">真实姓名</text>
          <input type="text" class="form-input" v-model="localData.name" placeholder="请输入真实姓名" />
        </view>
        <view class="form-item">
          <text class="form-label">球衣号码</text>
          <input type="number" class="form-input" v-model="localData.jerseyNumber" placeholder="0-999" maxlength="3" />
        </view>
      </view>

      <!-- 性别和位置行 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">性别</text>
          <radio-group @change="handleGenderChange" class="gender-selector">
            <label class="gender-option" :class="{ 'active': localData.sex === '1' }">
              <radio value="1" color="transparent" :checked="localData.sex === '1'" style="display:none;" />
              <text>男</text>
            </label>
            <label class="gender-option" :class="{ 'active': localData.sex === '2' }">
              <radio value="2" color="transparent" :checked="localData.sex === '2'" style="display:none;" />
              <text>女</text>
            </label>
          </radio-group>
        </view>
        <view class="form-item">
          <text class="form-label">场上位置</text>
          <view class="position-selector" @tap="showPositionPicker">
            <text :class="{ 'placeholder': !localData.position }">{{ getPositionText() }}</text>
            <uni-icons type="bottom" size="14" color="#c0c4cc"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 身高和体重行 -->
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">身高 (cm)</text>
          <input type="number" class="form-input" v-model="localData.height" placeholder="例如：185" />
        </view>
        <view class="form-item">
          <text class="form-label">体重 (kg)</text>
          <input type="number" class="form-input" v-model="localData.weight" placeholder="例如：75" />
        </view>
      </view>
    </view>

    <!-- 位置选择弹窗 -->
    <view v-if="showPicker" class="picker-overlay" @tap="hidePositionPicker">
      <view class="picker-container" @tap.stop="">
        <view class="picker-header">
          <text @tap="hidePositionPicker" class="picker-cancel">取消</text>
          <text class="picker-title">选择位置</text>
          <text @tap="confirmPosition" class="picker-confirm">确定</text>
        </view>
        <view class="picker-content">
          <view 
            v-for="(option, index) in positionOptions" 
            :key="option.value"
            class="picker-item"
            :class="{ 'selected': tempPosition === option.value }"
            @tap="selectTempPosition(option.value)"
          >
            <text>{{ option.text }}</text>
            <uni-icons v-if="tempPosition === option.value" type="checkmarkempty" size="16" color="#4a90e2"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { POSITION_OPTIONS } from '@/sheep/store/activity';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      avatar: '',
      name: '',
      jerseyNumber: '',
      sex: '1',
      position: null,
      height: '',
      weight: ''
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 本地数据
const localData = reactive({ ...props.modelValue });

// 位置选择相关
const positionOptions = POSITION_OPTIONS;
const showPicker = ref(false);
const tempPosition = ref(null);

// 监听本地数据变化并发射更新事件
watch(localData, (newValue) => {
  console.log('[OrderNewUserProfileFormSimple] Local data changed:', JSON.stringify(newValue));
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 监听外部传入的 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  console.log('[OrderNewUserProfileFormSimple] Props modelValue changed:', JSON.stringify(newValue));
  Object.assign(localData, newValue);
}, { deep: true });

// 获取位置文本
const getPositionText = () => {
  if (!localData.position) return '请选择位置';
  const option = positionOptions.find(p => p.value === Number(localData.position));
  return option ? option.text : '请选择位置';
};

// 显示位置选择器
const showPositionPicker = () => {
  tempPosition.value = localData.position;
  showPicker.value = true;
};

// 隐藏位置选择器
const hidePositionPicker = () => {
  showPicker.value = false;
};

// 选择临时位置
const selectTempPosition = (value) => {
  tempPosition.value = value;
};

// 确认位置选择
const confirmPosition = () => {
  localData.position = tempPosition.value;
  showPicker.value = false;
  console.log('[OrderNewUserProfileFormSimple] Position confirmed:', tempPosition.value);
};

// 处理性别选择
const handleGenderChange = (e) => {
  localData.sex = e.detail.value;
  console.log('[OrderNewUserProfileFormSimple] Gender changed to:', e.detail.value);
};

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      localData.avatar = res.tempFilePaths[0];
    }
  });
};
</script>

<style lang="scss" scoped>
/* 整体容器 */
.player-profile-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 头部样式 */
.profile-header {
  background: linear-gradient(135deg, #4a90e2, #5c6bc0);
  padding: 30rpx 32rpx;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -10%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.header-icon-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-left: 8rpx;
  color: #ffffff;
}

.header-desc {
  font-size: 24rpx;
  opacity: 0.9;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

/* 表单区域 */
.profile-form {
  padding: 40rpx 30rpx;
}

/* 头像上传区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}

.avatar-container {
  width: 180rpx;
  height: 180rpx;
  border-radius: 90rpx;
  border: 3rpx solid #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa, #f0f7ff);
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.15);
  transition: all 0.3s ease;
}

.avatar-container:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
}

.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-hint {
  font-size: 26rpx;
  color: #4a90e2;
  font-weight: 500;
}

/* 表单行和列 */
.form-row {
  display: flex;
  gap: 30rpx;
  margin-bottom: 36rpx;
}

.form-item {
  flex: 1;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 500;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 92rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4a90e2;
  background-color: #f8fbff;
  outline: none;
}

/* 性别选择器 */
.gender-selector {
  display: flex;
  width: 100%;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #ffffff;
}

.gender-option {
  flex: 1;
  height: 92rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #6c757d;
  background-color: #ffffff;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
}

.gender-option:first-child {
  border-right: 1rpx solid #e9ecef;
}

.gender-option.active {
  background: linear-gradient(135deg, #4a90e2, #5c6bc0);
  color: #ffffff;
  font-weight: 500;
}

.gender-option:active {
  transform: scale(0.98);
}

/* 位置选择器 */
.position-selector {
  width: 100%;
  height: 92rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #2c3e50;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.position-selector:active {
  background-color: #f8fbff;
  border-color: #4a90e2;
}

.position-selector .placeholder {
  color: #adb5bd;
}

/* 位置选择弹窗 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.picker-container {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 70vh;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  color: #4a90e2;
}

.picker-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #2c3e50;
}

.picker-content {
  padding: 20rpx 0;
  max-height: 50vh;
  overflow-y: auto;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  font-size: 32rpx;
  color: #2c3e50;
  transition: background-color 0.3s ease;
}

.picker-item:active {
  background-color: #f8f9fa;
}

.picker-item.selected {
  background-color: #f0f7ff;
  color: #4a90e2;
}
</style> 