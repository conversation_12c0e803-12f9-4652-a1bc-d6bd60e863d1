<template>
  <s-layout title="活动报名" navbar="team" :onShareAppMessage="shareInfo">
    <view class="activity-detail-page">
      <!-- 邀请码加入房间弹窗 -->
      <InviteJoinModal v-if="showInviteModal" :visible="showInviteModal" :inviteInfo="inviteInfo"
        @confirm="handleJoinFriendGroup" @cancel="handleCloseInviteModal" />

      <!-- 球队选择弹窗 -->
      <uni-popup ref="teamPopup" type="bottom" :safe-area="true" :mask-click="false">
        <view class="team-select-popup" @touchmove.stop.prevent>
          <view class="popup-header">
            <text class="popup-title">选择报名球队</text>
            <text class="popup-close" @click="handleCloseTeamSelect">×</text>
          </view>
          <view class="team-scroll-wrapper">
            <scroll-view class="team-list-container" scroll-y="true" :enhanced="true" :show-scrollbar="false"
              :bounces="false" :scroll-with-animation="true" :enable-passive="false" @touchmove.stop>
              <view class="team-list">
                <view v-for="team in userTeams" :key="team.id" class="team-item" @click="handleSelectTeam(team)">
                  <image class="team-logo" :src="sheep.$url.cdn(team.logo)" mode="aspectFit" />
                  <text class="team-name">{{ team.name }}</text>
                  <text class="team-count">{{ team.players?.length || 0 }}人</text>
                </view>
              </view>
              <view v-if="userTeams.length === 0" class="no-team-tip">
                <text>您还没有创建球队,请先创建球队</text>
                <button class="create-team-btn" @click="handleCreateTeam">创建球队</button>
              </view>
            </scroll-view>
          </view>
        </view>
      </uni-popup>

      <!-- 页面内容 -->
      <template v-if="activity">
        <!-- 统一头部区域 -->
        <ActivityHeader :activity="activity" @back="handleBack" @share="handleShare" />

        <!-- 主体内容区域 -->
        <view class="activity-content">
          <!-- 统一倒计时卡片 -->
          <SignupDeadlineCard class="signup-deadline-card" :deadline="activity.basicInfo?.signupDeadline"
            :status="activity.status" :activityType="activity.type" :note="getDeadlineNote()"
            :minPlayersToStart="getMinPlayersToStart()" />

          <!-- 条件渲染：排位赛内容 -->
          <template v-if="activity.type === ACTIVITY_TYPE.RANKING_MATCH">
            <!-- 比赛信息区块 -->
            <GameInfoSection :activityType="activity.type" :startTime="activity.basicInfo?.startTime"
              :duration="activity.gameConfig?.duration" :homeTeamColor="activity.gameConfig?.homeTeamColor"
              :awayTeamColor="activity.gameConfig?.awayTeamColor" :homeTeamName="activity.gameConfig?.homeTeamName"
              :awayTeamName="activity.gameConfig?.awayTeamName" :userTeamAssignment="userTeamAssignment" />

            <!-- 活动进度显示 -->
            <ActivityProgressSection :activityType="activity.type"
              :currentPlayers="activity.signupRelatedInfo?.currentPlayers || 0"
              :maxPlayers="activity.gameConfig?.maxPlayersTotal"
              :waitlistPlayers="activity.signupRelatedInfo?.waitlistPlayers || []"
              :homeTeamPlayers="activity.signupRelatedInfo?.homeTeamPlayers || []"
              :awayTeamPlayers="activity.signupRelatedInfo?.awayTeamPlayers || []"
              :homeTeamColor="activity.gameConfig?.homeTeamColor" :awayTeamColor="activity.gameConfig?.awayTeamColor"
              :homeTeamName="activity.gameConfig?.homeTeamName" :awayTeamName="activity.gameConfig?.awayTeamName" />

            <!-- 好友组队房间信息 -->
            <FriendTeamRoom
              v-if="activity.signupRelatedInfo?.friendTeamRoom && (activity.signupRelatedInfo?.currentUserSignupStatus === USER_SIGNUP_STATUS.PAID || activity.signupRelatedInfo?.currentUserSignupStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED)"
              :roomData="transformRoomData(activity.signupRelatedInfo?.friendTeamRoom)" mode="detail"
              @invite="handleInviteFriend" />
            <!-- 报名方式选择 -->
            <InfoSection title="报名方式" icon="compose"
              v-if="!activity.signupRelatedInfo?.friendTeamRoom && !isUserRegistered">
              <SignupOptions :selectedMode="signupMode"
                :isFriendGroupAvailable="activity.signupRelatedInfo?.isFriendGroupSignupAvailable"
                @change="handleSignupModeChange" />
            </InfoSection>

            <!-- 费用明细（个人） -->
            <ActivityFeeDetails :totalFee="activity.feeInfo?.totalFee"
              :venueFeeAmount="activity.feeInfo?.venueFeeAmount"
              :venueFeeDescription="activity.feeInfo?.venueFeeDescription"
              :supportFeeAmount="activity.feeInfo?.supportFeeAmount"
              :supportFeeDescription="activity.feeInfo?.supportFeeDescription" :feeType="'individual'" />

          </template>

          <!-- 条件渲染：友谊赛内容 -->
          <template v-else-if="activity.type === ACTIVITY_TYPE.FRIENDLY_MATCH">
            <!-- 比赛信息区块 -->
            <GameInfoSection :activityType="activity.type" :startTime="activity.basicInfo?.startTime"
              :duration="activity.gameConfig?.duration" :minPlayersPerTeam="activity.gameConfig?.minPlayersPerTeam"
              :maxPlayersPerTeam="activity.gameConfig?.maxPlayersPerTeam" />
            <!-- 活动进度显示 -->
            <ActivityProgressSection :activityType="activity.type"
              :currentTeams="activity.signupRelatedInfo?.successfulTeamsCount || 0"
              :maxTeams="activity.gameConfig?.maxTeams || 2"
              :signedUpTeams="activity.signupRelatedInfo?.signedUpTeams || []"
              :minPlayersPerTeam="activity.gameConfig?.minPlayersPerTeam || 5"
              :maxPlayersPerTeam="activity.gameConfig?.maxPlayersPerTeam || 15" />

            <!-- 费用明细（球队） -->
            <ActivityFeeDetails :totalFee="activity.feeInfo?.totalFee" :teamFee="activity.feeInfo?.teamFee"
              :venueFeeAmount="activity.feeInfo?.venueFeeAmount"
              :venueFeeDescription="activity.feeInfo?.venueFeeDescription"
              :supportFeeAmount="activity.feeInfo?.supportFeeAmount"
              :supportFeeDescription="activity.feeInfo?.supportFeeDescription" :feeType="'team'" />

          </template>

          <!-- 条件渲染：联赛内容 -->
          <template v-else-if="activity.type === ACTIVITY_TYPE.LEAGUE">
            <!-- 联赛信息卡片 -->
            <LeagueInfoSection :leagueInfo="activity.leagueInfo" :basicInfo="activity.basicInfo" />

            <!-- 活动进度显示 -->
            <ActivityProgressSection :activityType="activity.type"
              :currentTeams="activity.signupRelatedInfo?.successfulTeamsCount || 0"
              :minTeams="activity.gameConfig?.minTeams || 4" :maxTeams="activity.gameConfig?.maxTeams || 16"
              :signedUpTeams="activity.signupRelatedInfo?.signedUpTeams || []"
              :minPlayersPerTeam="activity.gameConfig?.minPlayersPerTeam || 5"
              :maxPlayersPerTeam="activity.gameConfig?.maxPlayersPerTeam || 15" />

            <!-- 赛制说明 -->
            <LeagueFormatSection :formatType="activity.leagueInfo?.formatType" :config="activity.leagueInfo?.config" />

            <!-- 费用明细（每人） -->
            <ActivityFeeDetails :totalFee="activity.feeInfo?.totalFee"
              :leagueFeePerPlayer="activity.feeInfo?.leagueFeePerPlayer"
              :venueFeeAmount="activity.feeInfo?.venueFeeAmount"
              :venueFeeDescription="activity.feeInfo?.venueFeeDescription"
              :supportFeeAmount="activity.feeInfo?.supportFeeAmount"
              :supportFeeDescription="activity.feeInfo?.supportFeeDescription" :feeType="'perPlayer'" />

          </template>

          <!-- 通用服务明细 -->
          <ActivityServiceDetails :servicesText="activity.basicInfo?.services" />

          <!-- 通用报名须知 -->
          <ActivitySignupNotice :noticeText="activity.basicInfo?.notice" :rulesText="activity.basicInfo?.rules"
            :activityType="activity.type" />

        </view>

        <!-- 统一底部操作栏 -->
        <BottomActionBar v-if="shouldShowActionBar" :activity="activity" :signupMode="signupMode"
          :isActivityFull="isActivityFull" :estimatedFee="getEstimatedFee()" @share="handleShare" @signup="handleSignup"
          @withdraw="handleWithdraw" @pay="handlePay" />
      </template>
    </view>
  </s-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onPullDownRefresh } from '@dcloudio/uni-app';
import { ACTIVITY_STATUS, ACTIVITY_TYPE, SIGNUP_MODE, USER_SIGNUP_STATUS } from '@/sheep/store/activity';
import ActivityApi from '@/sheep/api/operation/activity';
import FriendGroupApi from '@/sheep/api/operation/friendgroup';
import $router from '@/sheep/router';
import sheep from '@/sheep';

// 导入必需的组件
import ActivityHeader from './components/detail/ActivityHeader.vue';
import SignupDeadlineCard from './components/detail/SignupDeadlineCard.vue';
import InfoSection from './components/detail/InfoSection.vue';
import GameInfoSection from './components/detail/GameInfoSection.vue';
import BottomActionBar from './components/detail/BottomActionBar.vue';
import InviteJoinModal from './components/detail/InviteJoinModal.vue';

// 导入排位赛专用组件
import SignupOptions from './components/detail/SignupOptions.vue';
import FriendTeamRoom from './components/detail/FriendTeamRoom.vue';

// 导入联赛专用组件
import LeagueInfoSection from './components/detail/LeagueInfoSection.vue';
import LeagueFormatSection from './components/detail/LeagueFormatSection.vue';

// 导入通用进度组件
import ActivityProgressSection from './components/detail/ActivityProgressSection.vue';

// 导入通用组件
import ActivityFeeDetails from './components/detail/ActivityFeeDetails.vue';
import ActivityServiceDetails from './components/detail/ActivityServiceDetails.vue';
import ActivitySignupNotice from './components/detail/ActivitySignupNotice.vue';

// 导入球队相关API
import TeamApi from '@/sheep/api/operation/team';
import user from '@/sheep/store/user';

// 页面参数
const props = defineProps({
  id: {
    type: [Number, String],
    default: ''
  },
  invite_code: {
    type: String,
    default: ''
  },
  code: {  // 新增短参数名支持
    type: String,
    default: ''
  }
});

// 状态管理
const signupMode = ref(1); // 1-个人, 2-好友组队, 3-球队
const activity = ref(null);

// 邀请码相关状态
const showInviteModal = ref(false);
const inviteInfo = ref(null);

// 新增：球队选择相关状态
const teamPopup = ref(null);
const userTeams = ref([]);
const selectedTeam = ref(null);

// 分享类型标记 - 用于区分是哪种分享
const shareType = ref('normal'); // 'normal' | 'invite'

// 分享配置 - 根据分享类型动态生成分享信息
const shareInfo = computed(() => {
  // 如果没有活动数据，返回基本分享配置
  if (!activity.value) {
    return {
      title: '赛点篮球联盟',
      path: `/pages/activity/detail?id=${activityId.value || ''}`,
      imageUrl: ''
    };
  }

  // 根据分享类型返回不同的分享配置
  if (shareType.value === 'invite') {
    // 邀请分享：带邀请码的个性化分享
    const userStore = sheep.$store('user');
    const inviterName = userStore.userInfo?.nickname || '我';
    const inviteCode = activity.value.signupRelatedInfo?.friendTeamRoom?.inviteCode;

    if (inviteCode) {
      return {
        title: `${inviterName}邀请你参加${activity.value.basicInfo?.title}`,
        path: `/pages/activity/detail?id=${activityId.value}&code=${inviteCode}`,
        imageUrl: activity.value.basicInfo?.coverUrl || ''
      };
    }
  }

  // 默认普通分享配置
  return {
    title: activity.value.basicInfo?.title || '赛点篮球联盟',
    path: `/pages/activity/detail?id=${activityId.value}`,
    imageUrl: activity.value.basicInfo?.coverUrl || ''
  };
});

// 计算属性
const activityId = computed(() => props.id);

const isActivityFull = computed(() => {
  if (!activity.value || !activity.value.signupRelatedInfo || !activity.value.gameConfig) return false;

  // 修复：根据活动类型判断满员条件
  switch (activity.value.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      // 排位赛按人数判断
      const currentPlayers = activity.value.signupRelatedInfo.currentPlayers;
      const maxPlayers = activity.value.gameConfig.maxPlayersTotal;
      const isFull = currentPlayers >= maxPlayers;
      return isFull;
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      // 友谊赛按报名成功的球队数量判断
      const friendlySuccessfulTeams = activity.value.signupRelatedInfo?.successfulTeamsCount || 0;
      const friendlyMaxTeams = activity.value.gameConfig?.maxTeams || 2;
      return friendlySuccessfulTeams >= friendlyMaxTeams;
    case ACTIVITY_TYPE.LEAGUE:
      // 联赛按报名成功的球队数量判断
      const leagueSuccessfulTeams = activity.value.signupRelatedInfo?.successfulTeamsCount || 0;
      const leagueMaxTeams = activity.value.gameConfig?.maxTeams || 16;
      return leagueSuccessfulTeams >= leagueMaxTeams;
    default:
      return false;
  }
});

const shouldShowActionBar = computed(() => {
  if (!activity.value) return false;

  // 检查活动状态，对于组局失败、已取消、已结束、进行中状态，隐藏底部按钮
  const activityStatus = activity.value.status;
  if (activityStatus === ACTIVITY_STATUS.ONGOING || // 进行中
    activityStatus === ACTIVITY_STATUS.COMPLETED || // 已结束
    activityStatus === ACTIVITY_STATUS.CANCELED || // 已取消
    activityStatus === ACTIVITY_STATUS.GROUPING_FAILED) { // 组局失败
    return false;
  }

  const currentStatus = activity.value.signupRelatedInfo?.currentUserSignupStatus;

  // 待支付状态：显示立即支付按钮
  if (currentStatus === USER_SIGNUP_STATUS.PENDING_PAYMENT) {
    return true;
  }

  // 已支付/报名成功状态：显示退赛按钮
  if (currentStatus === USER_SIGNUP_STATUS.PAID ||
    currentStatus === USER_SIGNUP_STATUS.REGISTRATION_SUCCESS ||
    currentStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED) {
    return true;
  }

  // 活动报名中、报名已截止或组局成功：显示报名/候补按钮
  return activity.value.status === ACTIVITY_STATUS.ENROLLING ||
    activity.value.status === ACTIVITY_STATUS.ENROLLMENT_ENDED ||
    activity.value.status === ACTIVITY_STATUS.GROUPING_SUCCESSFUL; // 组局成功状态，允许候补
});

// 新增：判断用户是否已报名
const isUserRegistered = computed(() => {
  if (!activity.value || !activity.value.signupRelatedInfo) return false;
  const currentStatus = activity.value.signupRelatedInfo.currentUserSignupStatus;
  // 注意：PENDING_PAYMENT状态不算"已报名"，因为还没完成支付
  return currentStatus === USER_SIGNUP_STATUS.PAID ||
    currentStatus === USER_SIGNUP_STATUS.REGISTRATION_SUCCESS ||
    currentStatus === USER_SIGNUP_STATUS.FRIEND_GROUP_JOINED;
});

// 新增：获取用户的分队信息
const userTeamAssignment = computed(() => {
  if (!activity.value || !activity.value.signupRelatedInfo) return '';
  
  // 只对排位赛处理分队逻辑
  if (activity.value.type !== ACTIVITY_TYPE.RANKING_MATCH) return '';
  
  // 获取当前用户ID
  const userStore = sheep.$store('user');
  const currentUserId = userStore.userInfo?.id;
  if (!currentUserId) return '';
  
  const homeTeamPlayers = activity.value.signupRelatedInfo.homeTeamPlayers || [];
  const awayTeamPlayers = activity.value.signupRelatedInfo.awayTeamPlayers || [];
  
  // 检查用户是否在主队
  const isInHomeTeam = homeTeamPlayers.some(player => player.userId === currentUserId);
  if (isInHomeTeam) return 'home';
  
  // 检查用户是否在客队
  const isInAwayTeam = awayTeamPlayers.some(player => player.userId === currentUserId);
  if (isInAwayTeam) return 'away';
  
  // 用户未分队
  return '';
});

// 方法
const getDeadlineNote = () => {
  if (!activity.value) return '';

  switch (activity.value.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      return `报名人数满${activity.value.gameConfig?.minPlayersToStart || 16}人组局成功。组局失败，活动自动取消，费用将原路退回`;
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      return `需要${activity.value.gameConfig?.minTeams || 2}支球队报名才能组局成功。组局失败，活动自动取消，费用将原路退回`;
    case ACTIVITY_TYPE.LEAGUE:
      return `需要${activity.value.gameConfig?.minTeams || 4}支球队报名才能开赛。报名不足，联赛自动取消，费用将原路退回`;
    default:
      return '';
  }
};

const getMinPlayersToStart = () => {
  if (!activity.value) return 0;

  switch (activity.value.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      return activity.value.gameConfig?.minPlayersToStart || 16;
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      return activity.value.gameConfig?.minTeams || 2;
    case ACTIVITY_TYPE.LEAGUE:
      return activity.value.gameConfig?.minTeams || 4;
    default:
      return 0;
  }
};

const getEstimatedFee = () => {
  if (!activity.value) return 0;

  switch (activity.value.type) {
    case ACTIVITY_TYPE.RANKING_MATCH:
      return activity.value.signupRelatedInfo?.estimatedFeePerPlayer || 0;
    case ACTIVITY_TYPE.FRIENDLY_MATCH:
      // 友谊赛显示每队需要支付的费用（不是总费用）
      return activity.value.feeInfo?.teamFee || 0;
    case ACTIVITY_TYPE.LEAGUE:
      return activity.value.feeInfo?.leagueFeePerPlayer || 0;
    default:
      return 0;
  }
};

// 邀请码处理
const handleInviteCode = async () => {
  // 支持两种参数名：invite_code（完整名）和 code（短名）
  const inviteCode = props.invite_code || props.code;
  if (!inviteCode) return;

  try {
    console.log('[detail] 处理邀请码:', inviteCode, '来源:', props.invite_code ? 'invite_code' : 'code');
    const result = await FriendGroupApi.getInviteInfo(inviteCode);

    if (result.code === 0 && result.data) {
      // 将后端数据转换为前端组件期望的格式
      inviteInfo.value = {
        ...result.data,
        leaderName: result.data.leaderNickname,   // 映射字段名
        leaderAvatar: result.data.leaderAvatarUrl, // 映射字段名
        roomId: result.data.friendGroupId  // 将friendGroupId映射为roomId，供订单页使用
      };
      console.log('[detail] 转换后的邀请信息:', inviteInfo.value);
      showInviteModal.value = true;
    } else {
      uni.showToast({
        title: '邀请码无效或已过期',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('[detail] 获取邀请信息失败:', error);
    uni.showToast({
      title: '邀请码处理失败',
      icon: 'none'
    });
  }
};

const handleJoinFriendGroup = () => {
  showInviteModal.value = false;

  // 直接跳转到订单页面，携带邀请码和房间ID
  const inviteCode = props.invite_code || props.code;
  $router.go('/pages/activity/confirm-order', {
    activityId: activityId.value,
    activityType: ACTIVITY_TYPE.RANKING_MATCH,
    signupMode: SIGNUP_MODE.FRIEND_GROUP,
    roomId: inviteInfo.value?.roomId,  // 订单页使用的是roomId
    invite_code: inviteCode
  });
};

const handleCloseInviteModal = () => {
  showInviteModal.value = false;
  inviteInfo.value = null;
};

// 数据转换方法
const transformRoomData = (friendTeamRoom) => {
  if (!friendTeamRoom) return {};

  const allMembers = friendTeamRoom.members || [];

  // 使用userId来识别房主
  const ownerMember = allMembers.find(member => member.userId === friendTeamRoom.ownerUserId);
  const otherMembers = allMembers.filter(member => member.userId !== friendTeamRoom.ownerUserId);

  return {
    ...friendTeamRoom,
    owner: ownerMember ? {
      userId: ownerMember.userId,
      nickname: ownerMember.nickname,
      avatar: ownerMember.avatar
    } : {
      userId: friendTeamRoom.ownerUserId,
      nickname: '房主',
      avatar: null
    },
    members: otherMembers.map(member => ({
      userId: member.userId,
      nickname: member.nickname,
      avatar: member.avatar
    })),
    maxMembers: friendTeamRoom.maxMembers || 3
  };
};

// 其他事件处理
const handleSignupModeChange = (mode) => {
  signupMode.value = mode;
};

const handleSignup = () => {
  console.log('[detail] handleSignup called, activity:', activity.value);
  if (!activity.value) return;

  console.log('[detail] 活动类型:', activity.value.type);
  console.log('[detail] FRIENDLY_MATCH:', ACTIVITY_TYPE.FRIENDLY_MATCH);
  console.log('[detail] LEAGUE:', ACTIVITY_TYPE.LEAGUE);

  // 根据活动类型处理报名逻辑
  if (activity.value.type === ACTIVITY_TYPE.FRIENDLY_MATCH || activity.value.type === ACTIVITY_TYPE.LEAGUE) {
    // 友谊赛和联赛需要选择球队
    console.log('[detail] 需要选择球队，调用 handleShowTeamSelect');
    handleShowTeamSelect();
    return;
  }

  console.log('[detail] 排位赛报名，跳转到订单页面');
  // 排位赛的原有逻辑
  const params = {
    activityId: activityId.value,
    activityType: activity.value.type,
    signupMode: signupMode.value
  };

  $router.go('/pages/activity/confirm-order', params);
};

const handleShare = () => {
  // 立即设置为普通分享类型
  shareType.value = 'normal';
  console.log('[handleShare] 设置为普通分享');
  console.log('[handleShare] 分享配置:', JSON.stringify(shareInfo.value, null, 2));
};

const handleInviteFriend = () => {
  // 检查是否有邀请码
  if (!activity.value?.signupRelatedInfo?.friendTeamRoom?.inviteCode) {
    uni.showToast({
      title: '邀请码不存在，无法邀请好友',
      icon: 'none'
    });
    return;
  }

  // 立即设置为邀请分享类型
  shareType.value = 'invite';
  console.log('[handleInviteFriend] 设置为邀请分享');
  console.log('[handleInviteFriend] 分享配置:', JSON.stringify(shareInfo.value, null, 2));
};

const handleBack = () => {
  uni.navigateBack();
};

// 新增：处理支付事件
const handlePay = () => {
  if (!activity.value || !activity.value.signupRelatedInfo) return;

  // 获取报名记录ID
  const registrationId = activity.value.signupRelatedInfo.registrationId;
  if (!registrationId) {
    uni.showToast({
      title: '报名信息不存在，无法支付',
      icon: 'none'
    });
    return;
  }

  // 获取支付订单ID
  const payOrderId = activity.value.signupRelatedInfo.payOrderId;
  if (!payOrderId) {
    uni.showToast({
      title: '支付订单不存在，请重新报名',
      icon: 'none'
    });
    return;
  }

  // 跳转到支付页面
  $router.go('/pages/pay/index', {
    id: payOrderId,
    registrationId: registrationId
  });
};

// 新增：处理退赛事件
const handleWithdraw = async () => {
  if (!activity.value || !activity.value.signupRelatedInfo) return;

  // 获取报名记录ID
  const registrationId = activity.value.signupRelatedInfo.registrationId;
  if (!registrationId) {
    uni.showToast({
      title: '报名信息不存在，无法退赛',
      icon: 'none'
    });
    return;
  }

  // 直接显示退赛原因选择，包含"不填写原因"选项
  const reasonOptions = [
    '临时有事无法参加',
    '身体不适',
    '工作冲突',
    '时间冲突',
    '不填写原因'
  ];

  uni.showActionSheet({
    title: '请选择退赛原因',
    itemList: reasonOptions,
    success: async (res) => {
      const selectedReason = reasonOptions[res.tapIndex];
      const cancelReason = selectedReason === '不填写原因' ? '' : selectedReason;

      // 显示最终确认
      uni.showModal({
        title: '确认退赛',
        content: `确定要退赛吗？${cancelReason ? `\n退赛原因：${cancelReason}` : ''}\n\n退赛后将根据活动规则进行退款处理。`,
        confirmText: '确认退赛',
        cancelText: '取消',
        confirmColor: '#ff3b30',
        success: async (confirmRes) => {
          if (confirmRes.confirm) {
            await submitWithdrawal(registrationId, cancelReason);
          }
        }
      });
    }
  });
};

// 新增：提交退赛申请的统一方法
const submitWithdrawal = async (registrationId, cancelReason) => {
  try {
    uni.showLoading({
      title: '退赛处理中...'
    });

    const response = await ActivityApi.cancelRegistration(registrationId, cancelReason);

    if (response.code === 0) {
      uni.hideLoading();
      uni.showToast({
        title: '退赛申请成功',
        icon: 'success'
      });

      // 刷新活动详情数据
      setTimeout(() => {
        fetchActivityDetail();
      }, 1000);
    } else {
      uni.hideLoading();
      uni.showToast({
        title: response.msg || '退赛申请失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('[detail] 退赛申请失败:', error);

    // 根据错误信息给出友好提示
    let errorMessage = '退赛申请失败';
    if (error.message) {
      errorMessage = error.message;
    } else if (error.msg) {
      errorMessage = error.msg;
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
  }
};

// 获取活动详情
const fetchActivityDetail = async () => {
  if (!activityId.value) return;

  try {
    // 构建API调用URL，支持邀请码参数
    const params = {};

    // 如果有邀请码，添加到请求参数中（支持两种参数名）
    const inviteCode = props.invite_code || props.code;
    if (inviteCode) {
      params.inviteCode = inviteCode;
    }

    const result = await ActivityApi.getActivityDetail(activityId.value, params);

    if (result.code === 0) {
      activity.value = result.data;

      // 注意：不在这里初始化 currentShareConfig，让它保持空值
      // shareInfo 计算属性会根据 currentShareConfig 是否有值来决定返回什么
      // 这样能确保分享配置真正反映最后一次用户操作

      // 处理邀请码（在活动数据加载完成后）
      await handleInviteCode();
    } else {
      uni.showToast({
        title: result.msg || '获取活动详情失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('[detail] 获取活动详情失败:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  }
};

// 重置分享类型为默认状态
const resetShareType = () => {
  shareType.value = 'normal';
};

// 新增：获取用户球队列表
const getUserTeams = async () => {
  console.log('[detail] getUserTeams called');
  const userStore = user();
  const playerInfo = userStore.playerInfo;

  console.log('[detail] playerInfo:', playerInfo);
  if (!playerInfo) {
    console.log('[detail] 未获取到球员信息');
    uni.showToast({
      title: '请先完善球员信息',
      icon: 'none'
    });
    return;
  }

  const playerId = playerInfo.playerId;
  console.log('[detail] playerId:', playerId);
  if (!playerId) {
    console.log('[detail] 未获取到球员ID');
    uni.showToast({
      title: '球员信息不完整',
      icon: 'none'
    });
    return;
  }

  try {
    const { code, data } = await TeamApi.getTeamOfPlayer({
      playerId
    });
    if (code === 0) {
      userTeams.value = data || [];
    } else {
      console.error('[detail] 获取球队列表失败，code:', code);
    }
  } catch (error) {
    console.error('[detail] 获取球队列表失败:', error);
    uni.showToast({
      title: '获取球队列表失败',
      icon: 'none'
    });
  }
};

// 新增：处理球队选择弹窗
const handleShowTeamSelect = async () => {
  console.log('[detail] 显示球队选择弹窗');
  await getUserTeams();
  if (teamPopup.value) {
    teamPopup.value.open();
  }
};

const handleCloseTeamSelect = () => {
  console.log('[detail] 关闭球队选择弹窗');
  if (teamPopup.value) {
    teamPopup.value.close();
  }
};

const handleSelectTeam = (team) => {
  console.log('[detail] 选择球队:', team);
  // 检查球队是否有队长
  if (!team.captain) {
    uni.showToast({
      title: '请先设置球队队长',
      icon: 'none'
    });
    return;
  }

  selectedTeam.value = team;
  handleCloseTeamSelect();

  // 跳转到订单页面，携带球队信息
  $router.go('/pages/activity/confirm-order', {
    activityId: activityId.value,
    activityType: activity.value.type,
    signupMode: SIGNUP_MODE.TEAM,
    teamId: team.id,
    teamName: team.name,
    teamLogo: team.logo
  });
};

const handleCreateTeam = () => {
  handleCloseTeamSelect();
  $router.go('/pages/team/create-team');
};

// 下拉刷新处理
const handlePullDownRefresh = async () => {
  console.log('[detail] 触发下拉刷新');
  try {
    // 重新获取活动详情数据
    await fetchActivityDetail();
    console.log('[detail] 下拉刷新完成');
    
    // 显示简洁的刷新成功提示
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 800
    });
  } catch (error) {
    console.error('[detail] 下拉刷新失败:', error);
    // 显示刷新失败提示
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none',
      duration: 1500
    });
  } finally {
    // 停止下拉刷新动画
    uni.stopPullDownRefresh();
  }
};

// 生命周期
onMounted(async () => {
  console.log('[detail] 页面加载参数:', {
    id: props.id,
    invite_code: props.invite_code,
    code: props.code
  });

  // 初始化分享类型
  resetShareType();

  // #ifdef MP-WEIXIN
  // 启用微信小程序分享功能
  wx.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage'] // 只启用分享给朋友
  });
  // #endif

  if (props.id) {
    await fetchActivityDetail();
  }
});

// 页面下拉刷新事件
onPullDownRefresh(() => {
  handlePullDownRefresh();
});

// 根据官方文档，在 Vue 3 中定义页面级别的分享事件监听


</script>

<style lang="scss" scoped>
.activity-detail-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background: #f5f5f5;
}

.activity-content {
  padding: 20rpx 30rpx 150rpx;
  background: #f5f5f5;

  .signup-deadline-card {
    margin-top: 0;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background: #f5f5f5;

  text {
    margin-bottom: 40rpx;
    color: #666;
  }

  button {
    padding: 20rpx 40rpx;
    background: var(--ui-BG-Main);
    color: var(--ui-TC-Main);
    border: none;
    border-radius: 10rpx;
  }
}

.team-select-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  // 使用更安全的高度计算方式，避免在不同机型下被遮挡
  height: calc(65vh - env(safe-area-inset-bottom));
  max-height: 580rpx;
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // 确保在所有机型下都有合适的底部间距
  padding-bottom: max(20rpx, env(safe-area-inset-bottom));

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx 30rpx;
    flex-shrink: 0;
    border-bottom: 1rpx solid #f0f0f0;
    background-color: #fff;
    z-index: 1;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
    }

    .popup-close {
      font-size: 40rpx;
      color: #999;
      padding: 10rpx;
      border-radius: 50%;

      &:active {
        background-color: #f5f5f5;
      }
    }
  }

  .team-scroll-wrapper {
    flex: 1;
    overflow: hidden;
    // 确保滚动容器有足够的空间
    min-height: 0;

    .team-list-container {
      height: 100%;
      width: 100%;

      .team-list {
        // 为球队列表添加底部内边距，确保最后一项可见
        padding-bottom: 40rpx;

        .team-item {
          display: flex;
          align-items: center;
          padding: 20rpx;
          border-bottom: 1rpx solid #f0f0f0;
          transition: background-color 0.2s ease;

          .team-logo {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 30rpx;
            background-color: #f5f5f5;
            object-fit: cover;
            flex-shrink: 0;
          }

          .team-name {
            flex: 1;
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 20rpx;
          }

          .team-count {
            font-size: 26rpx;
            color: #999;
            flex-shrink: 0;
          }

          &:active {
            background-color: #f8f9fa;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .no-team-tip {
        padding: 60rpx 30rpx 80rpx 30rpx;
        text-align: center;
        color: #999;
        font-size: 28rpx;

        .create-team-btn {
          margin-top: 30rpx;
          background: linear-gradient(135deg, #4A90E2, #5c6bc0);
          color: #fff;
          border-radius: 50rpx;
          width: 300rpx;
          height: 88rpx;
          line-height: 88rpx;
          font-size: 30rpx;
          border: none;
          box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);

          &:active {
            transform: translateY(2rpx);
            box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
          }
        }
      }
    }
  }
}
</style>