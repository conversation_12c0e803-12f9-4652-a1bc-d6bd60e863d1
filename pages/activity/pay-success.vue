<template>
  <view class="pay-success-page">
    <view class="success-icon">
      <u-icon name="checkmark-circle" color="#09BE4F" size="80"></u-icon>
    </view>
    
    <view class="success-title">报名成功</view>
    <view class="success-desc">您已成功报名参加此活动</view>
    
    <view class="activity-card" v-if="activity">
      <view class="activity-title">{{ activity.title }}</view>
      <view class="activity-info">
        <view class="info-item">
          <u-icon name="calendar" size="14" color="#666"></u-icon>
          <text>{{ activity.activityTime }}</text>
        </view>
        <view class="info-item">
          <u-icon name="map" size="14" color="#666"></u-icon>
          <text>{{ activity.location?.name }}</text>
        </view>
      </view>
    </view>
    
    <view class="tips-section">
      <view class="tips-title">温馨提示</view>
      <view class="tips-list">
        <view class="tip-item">
          <u-icon name="info-circle" size="16" color="#2979ff"></u-icon>
          <text>比赛当天请提前30分钟到达场地</text>
        </view>
        <view class="tip-item">
          <u-icon name="info-circle" size="16" color="#2979ff"></u-icon>
          <text>请携带个人装备，保持良好状态</text>
        </view>
        <view class="tip-item">
          <u-icon name="info-circle" size="16" color="#2979ff"></u-icon>
          <text>如需取消报名，请至少提前24小时操作</text>
        </view>
      </view>
    </view>
    
    <view class="share-section">
      <button class="share-btn" open-type="share">
        <u-icon name="share" size="16" color="#fff"></u-icon>
        <text>分享给朋友</text>
      </button>
      <text class="share-tip">邀请好友一起参加</text>
    </view>
    
    <view class="action-btns">
      <button class="btn secondary" @click="goToActivityDetail">查看活动详情</button>
      <button class="btn primary" @click="goToMyActivities">我的活动</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useSheep } from '@/sheep/hooks/useSheep';

const props = defineProps({
  activityId: {
    type: [String, Number],
    required: true
  }
});

const { request } = useSheep();
const activity = ref(null);
const loading = ref(true);

// 获取活动详情
const fetchActivityDetail = async () => {
  loading.value = true;
  try {
    const res = await request({
      url: '/activity/detail',
      method: 'GET',
      data: {
        id: props.activityId
      }
    });
    
    if (res.code === 0 && res.data) {
      activity.value = res.data;
    } else {
      uni.showToast({
        title: '获取活动信息失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取活动信息失败', error);
    uni.showToast({
      title: '获取活动信息失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 跳转到活动详情页
const goToActivityDetail = () => {
  uni.redirectTo({
    url: `/pages/activity/detail?id=${props.activityId}`
  });
};

// 跳转到我的活动页面
const goToMyActivities = () => {
  uni.switchTab({
    url: '/pages/user/my-activities'
  });
};

// 分享配置
const onShareAppMessage = () => {
  if (!activity.value) return {};
  
  return {
    title: `我刚刚报名了「${activity.value.title}」，一起来参加吧！`,
    path: `/pages/activity/detail?id=${props.activityId}&share=1`,
    imageUrl: activity.value.cover || ''
  };
};

onMounted(() => {
  fetchActivityDetail();
});

defineExpose({
  onShareAppMessage
});
</script>

<style lang="scss" scoped>
.pay-success-page {
  min-height: 100vh;
  padding: 40rpx 30rpx;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  margin-top: 60rpx;
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.activity-card {
  width: 90%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .activity-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .activity-info {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    
    .info-item {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #666;
      
      text {
        margin-left: 10rpx;
      }
    }
  }
}

.tips-section {
  width: 90%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  
  .tips-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .tips-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    
    .tip-item {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #666;
      
      text {
        margin-left: 10rpx;
      }
    }
  }
}

.share-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  
  .share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300rpx;
    height: 80rpx;
    background-color: #2979ff;
    border-radius: 40rpx;
    color: #fff;
    font-size: 28rpx;
    
    text {
      margin-left: 10rpx;
    }
  }
  
  .share-tip {
    margin-top: 16rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.action-btns {
  width: 90%;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  
  .btn {
    width: 320rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    text-align: center;
    
    &.primary {
      background-color: #2979ff;
      color: #fff;
    }
    
    &.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #e0e0e0;
    }
  }
}
</style> 