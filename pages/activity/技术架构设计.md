# 活动报名系统技术架构设计文档

## 文档目录

1. **概述与架构原则**
   - 1.1 系统概述
   - 1.2 架构设计原则
   - 1.3 技术栈选择

2. **系统整体架构**
   - 2.1 系统架构图
   - 2.2 数据流向设计
   - 2.3 模块依赖关系

3. **数据库设计**
   - 3.1 数据表结构
   - 3.2 索引设计
   - 3.3 数据流转逻辑

4. **API接口设计**
   - 4.1 接口规范
   - 4.2 核心接口设计
   - 4.3 错误处理机制

5. **支付集成架构**
   - 5.1 支付流程设计
   - 5.2 支付SDK集成
   - 5.3 支付状态管理

6. **安全机制**
   - 6.1 数据安全
   - 6.2 业务安全
   - 6.3 系统安全

7. **前端技术实现架构**
   - 7.1 页面架构设计
   - 7.2 组件复用策略
   - 7.3 状态管理架构
   - 7.4 API交互设计
   - 7.5 错误处理机制
   - 7.6 性能优化策略
   - 7.7 支付流程实现
   - 7.8 开发规范
   - 7.9 业务逻辑实现要点

8. **UI设计规范**
   - 8.1 设计系统概述
   - 8.2 色彩系统
   - 8.3 排版系统
   - 8.4 间距与布局
   - 8.5 组件样式规范
   - 8.6 页面布局模板
   - 8.7 状态样式设计
   - 8.8 动画与过渡规范

---

# 赛点篮球技术架构设计 (基于实际实现)

## 1. 概述

本文档基于赛点篮球活动报名系统的实际实现，详细描述了在现有 uniapp 项目中的前端架构。项目采用 Vue 3 Composition API，实现了排位赛、友谊赛、联赛等多种活动类型的报名、支付、展示功能，并通过组件化和 Composable 模式实现了高度的代码复用和可维护性。

**设计原则**: 在 Vue 3 Composition API 的基础上，融入面向对象 (OOP) 的设计思想，强调封装、内聚和复用。

## 2. 前端技术栈

-   **框架**: uniapp (Vue 3 语法)
-   **状态管理**: Composable 模式 + 响应式状态管理
-   **UI**: 基于 `sheep/ui` 的自定义 UI 组件和第三方组件库 (uni-ui)
-   **开发模式**: Vue 3 Composition API
-   **路由**: uniapp 页面路由
-   **API 请求**: `sheep/api` 和 `sheep/request`
-   **底层库**: 依赖 `sheep` 提供的各项能力

## 3. 实际页面结构

### 3.1 核心页面

**活动详情页 (`/pages/activity/detail.vue`)**:
- **展示不同活动类型**的详细信息和报名入口
- 支持排位赛、友谊赛、联赛的差异化展示
- 包含邀请码访问逻辑和好友组队功能

**确认订单页 (`/pages/activity/confirm-order.vue`)**:
- **核心统一页面**，整合所有活动类型的报名确认逻辑
- **路由参数**: 
  ```javascript
  {
    activityId: String|Number,      // 活动ID
    activityType: String|Number,    // 活动类型
    signupMode: String|Number,      // 报名模式
    isNewUser: String|Boolean,      // 是否新用户
    teamId: String|Number,          // 球队ID（可选）
    roomId: String|Number           // 房间ID（可选）
  }
  ```

**支付页面 (`/pages/pay/index.vue`)**:
- **收银台页面**，处理支付方式选择和支付操作
- 支持微信支付、支付宝支付、余额支付等多种支付方式
- 通过 `sheep/platform/pay.js` 统一处理支付逻辑

**支付结果页 (`/pages/pay/result.vue`)**:
- **统一支付结果展示页面**，显示支付成功/失败状态
- 根据订单类型（`orderType`）显示不同的后续操作按钮
- 整合原 `pay-success.vue` 的活动报名成功逻辑

**活动列表页 (`/pages/activity/list.vue`)**:
- 活动列表展示和筛选功能
- **动态内容**: 
  - 根据 `isNewUser` 显示新用户球员档案表单或现有用户信息
  - 根据 `signupMode` 显示个人信息、好友组队信息或球队信息
  - 根据 `activityType` 动态展示费用构成和支付方式选择
  - 通用展示活动信息、协议勾选、支付方式选择
- **核心逻辑**: 由 `useConfirmOrder.js` Composable 驱动

### 3.2 组件化架构

#### 3.2.1 确认订单页面组件 (`/pages/activity/components/`)

#### 3.2.1 确认订单页面组件

**核心组件**:

```
OrderActivityInfoCard.vue        # 活动信息展示卡片
├── 活动标题、类型标签
├── 时间、地点信息
├── 活动状态和类型徽章
└── 现代化卡片设计

OrderNewUserProfileForm.vue      # 新用户球员档案表单
├── 渐变头部设计
├── 表单字段验证
├── 现代化交互体验
└── 响应式布局

OrderFeeDetailsCard.vue          # 费用明细卡片
├── 费用构成展示
├── 优惠券选择器
├── 积分使用开关
└── 实时价格计算

OrderTeamInfo.vue               # 球队信息展示
├── 球队logo和信息
├── 成员列表展示
├── 选择球队功能
└── 错误状态处理

OrderBottomBar.vue              # 底部操作栏
├── 最终金额显示
├── 提交按钮状态管理
├── 协议检查集成
└── 加载状态处理

OrderAgreementSection.vue       # 协议勾选组件
├── 协议checkbox
├── 协议链接跳转
└── 状态同步

OrderPaymentSection.vue         # 支付方式选择
├── 球队费用支付方式
├── AA制和队长垫付选择
└── 费用说明展示

FriendTeamRoom.vue              # 好友组队房间
├── detail模式（详情页）
├── simple模式（确认页）
├── 房间状态展示
└── 成员信息管理
```

#### 3.2.2 活动详情页面组件

**详情页特有组件**:

```
ActivityHeader.vue              # 活动头部
├── 背景图片展示
├── 返回和分享按钮
└── 活动基本信息

SignupDeadlineCard.vue          # 报名截止倒计时
├── 倒计时显示
├── 组局说明
└── 状态提示

InfoSection.vue                 # 信息展示区块
├── 通用信息容器
├── 图标和标题
└── 可扩展内容区

SignupOptions.vue               # 报名方式选择
├── 个人报名选项
├── 好友组队选项
└── 交互状态管理

ActivitySignupProgress.vue      # 排位赛报名进度
├── 主队/客队人数显示
├── 候补列表展示
└── 球员头像列表

FriendlyMatchSignupProgress.vue # 友谊赛报名进度
├── 球队报名状态
├── 球队信息展示
└── 报名数量统计

ActivityFeeDetails.vue          # 费用详情
ActivityServiceDetails.vue      # 服务详情
ActivitySignupNotice.vue        # 报名须知
```

**辅助组件**:
- **OrderLoadingState.vue**: 统一的加载状态展示
- **OrderErrorState.vue**: 统一的错误状态展示
- **SimpleRiskModal.vue**: 风险协议书弹窗
- **OrderFeeExplanation.vue**: 费用说明组件
- **BottomActionBar.vue**: 底部操作栏（详情页用）

#### 3.2.2 组件复用设计亮点

**FriendTeamRoom 组件**是组件复用的典型示例：
```vue
<!-- 详情页使用 -->
<FriendTeamRoom 
  :roomData="roomInfo" 
  mode="detail" 
  @join-room="handleJoinRoom"
  @invite-friend="handleInviteFriend" 
/>

<!-- 确认订单页使用 -->
<FriendTeamRoom 
  :roomData="friendGroupInfo" 
  mode="simple" 
/>
```

**复用优势**:
- 通过 `mode` 属性控制显示详细程度
- 避免重复开发相似功能
- 统一视觉风格和交互体验
- 提高代码维护效率

## 4. 业务逻辑架构 (Composable 模式)

### 4.1 核心 Composable

**`useConfirmOrder.js`**: 确认订单页面的业务逻辑封装

**架构设计**:
```javascript
export default function useConfirmOrder(routeParams) {
  // ===== 响应式状态 =====
  const loading = ref(true)
  const activity = ref(null)
  const feeInfo = ref({})
  // ... 其他状态

  // ===== 计算属性 =====
  const isReady = computed(() => {
    return !loading.value && activity.value
  })
  
  const canSubmit = computed(() => {
    return isReady.value && agreementChecked.value && !submitting.value
  })
  // ... 其他计算属性

  // ===== 核心方法 =====
  const init = async () => {
    // 数据初始化逻辑
  }

  const recalculateOrderPrice = (type, value) => {
    // 智能价格重算
  }

  const handleSubmit = async () => {
    // 订单提交逻辑
  }

  // ===== 返回接口 =====
  return {
    // 状态
    loading, activity, feeInfo,
    // 计算属性  
    isReady, canSubmit,
    // 方法
    init, recalculateOrderPrice, handleSubmit
  }
}
```

**核心功能**:

1. **智能数据初始化**:
        ```javascript
   const init = async () => {
     try {
       loading.value = true
       // 获取活动信息
       await fetchActivityInfo()
       // 获取用户档案
       await fetchUserProfile()
       // 获取费用信息
       await fetchFeeDetails()
       // 检查特殊状态（好友组队、球队等）
       await handleSpecialModes()
     } catch (error) {
       // 错误处理和降级方案
       console.error('初始化失败:', error)
       loadMockData() // 提供Mock数据确保页面可用
     } finally {
       loading.value = false
     }
   }
   ```

2. **智能价格重算**:
   ```javascript
   const recalculateOrderPrice = (type, value) => {
     // 只有优惠券和积分变更时才重算价格
     if (type === 'coupon' || type === 'points') {
       console.log(`[useConfirmOrder] 重新计算价格: ${type} = ${value}`)
       callSettlementAPI({ [type]: value })
     }
   }
   ```

3. **严格的协议检查**:
        ```javascript
   const handleSubmit = async () => {
     // 优先检查协议状态
     if (!agreementChecked.value) {
       sheep.$helper.toast('请先阅读并同意《风险协议书》')
       return
     }
     // 执行提交逻辑
   }
   ```

**优化亮点**:
- **状态局部化**: 避免全局状态污染
- **智能重算**: 减少不必要的网络请求
- **错误降级**: 确保页面在异常情况下的可用性
- **类型安全**: 完善的参数验证和错误处理

### 4.2 实际状态管理架构

**混合状态管理模式**:

本项目采用了 **Pinia Store + Composable** 的混合模式：

- **Pinia Store (`sheep/store/operation/activity.js`)**: 管理全局活动状态和枚举
- **Composable (`sheep/hooks/useConfirmOrder.js`)**: 管理页面级业务逻辑

**Pinia Store 职责**:
```javascript
// 全局枚举和常量
export const ACTIVITY_TYPE = {
  RANKING_MATCH: 1,  // 排位赛
  FRIENDLY_MATCH: 2, // 友谊赛
  LEAGUE: 3         // 联赛
};

export const SIGNUP_MODE = {
  INDIVIDUAL: 1,   // 个人报名
  FRIEND_GROUP: 2, // 好友组队
  TEAM: 3          // 球队报名
};

// 全局状态管理
const useActivityStore = defineStore('activity', {
  state: () => ({
    activityList: [],
    currentActivityDetail: null,
    currentRoomInfo: null,
  }),
  // getters 和 actions
});
```

**Composable 职责**:
```javascript
// 页面级业务逻辑
export default function useConfirmOrder(routeParams) {
  // 响应式状态
  const loading = ref(true);
  const activity = ref(null);
  
  // 计算属性
  const isReady = computed(() => !loading.value && activity.value);
  
  // 业务方法
  const init = async () => { /* 初始化逻辑 */ };
  
  return { loading, activity, isReady, init };
}
```

**混合模式优势**:
- ✅ 全局状态统一管理，避免重复定义
- ✅ 页面状态局部化，避免状态污染
- ✅ 类型安全和更好的开发体验
- ✅ 更灵活的逻辑组合

## 5. API 架构设计

### 5.1 后端接口对接

**完整支付流程**:

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 确认订单页<br/>confirm-order.vue
    participant API as 后端API
    participant P as 支付页面<br/>pay/index.vue
    participant PS as 支付系统<br/>pay.js
    participant R as 支付结果页<br/>pay/result.vue

    U->>C: 进入确认页
    C->>API: 调用结算接口
    API-->>C: 返回初始数据
    C->>U: 显示页面内容
    
    U->>C: 选择优惠券/积分
    C->>API: 重新调用结算接口
    API-->>C: 返回更新后价格
    
    U->>C: 提交订单
    C->>API: 调用创建订单接口
    API-->>C: 返回支付订单ID
    C->>P: 跳转到支付页面
    
    P->>API: 获取支付订单信息
    API-->>P: 返回支付方式和订单详情
    P->>U: 显示支付方式选择
    
    U->>P: 选择支付方式并确认
    P->>PS: 调用支付平台
    PS->>PS: 处理支付（微信/支付宝/余额）
    PS-->>R: 支付完成，跳转结果页
    
    R->>API: 轮询支付结果
    API-->>R: 返回支付状态
    R->>U: 显示支付结果和后续操作
```

**接口详情**:

1. **结算接口** `/operation/registration/settlement`:
        ```javascript
   // 请求
   {
     activityId: 123,
     teamId: 456,           // 可选
     couponId: 789,         // 可选
     usePoints: true,       // 可选
     playerProfile: {...}   // 新用户必需
   }
   
   // 响应
   {
     isNewUser: false,
     activityInfo: {...},
     feeInfo: {
       totalAmount: 10000,
       payAmount: 8500,
       couponDiscountAmount: 1000,
       pointsDiscountAmount: 500
     },
     availableCoupons: [...],
     userPointsInfo: {...}
   }
   ```

2. **创建订单接口** `/operation/registration/create`:
   ```javascript
   // 请求
   {
     activityId: 123,
     teamId: 456,
     couponId: 789,
     usePoints: true,
     paymentType: 1,
     playerProfile: {...}
   }
   
   // 响应
   {
     orderId: "ORDER_123456",
     paymentParams: {...},
     redirectUrl: "/pages/pay/index"
   }
   ```

### 5.2 错误处理架构

**错误码系统**:
```java
// 球员相关错误码
ErrorCode PLAYER_PROFILE_REQUIRED = new ErrorCode(1_001_002_004, "新用户需要先完善球员档案信息");
ErrorCode PLAYER_NOT_FOUND_BY_USER = new ErrorCode(1_001_002_001, "未找到用户对应的球员信息");

// 活动相关错误码  
ErrorCode ACTIVITY_NOT_EXISTS = new ErrorCode(1_001_000_001, "活动不存在");
ErrorCode REGISTRATION_ALREADY_EXISTS = new ErrorCode(1_001_001_001, "您已报名该活动，请勿重复报名");
```

**前端错误处理**:
    ```javascript
const handleAPIError = (error) => {
  const errorCode = error.code
  const errorMsg = error.message
  
  switch (errorCode) {
    case 1_001_002_004: // PLAYER_PROFILE_REQUIRED
      // 引导用户完善档案
      showProfileForm()
      break
    case 1_001_001_001: // REGISTRATION_ALREADY_EXISTS
      // 跳转到已有订单页面
      redirectToExistingOrder()
      break
    default:
      // 通用错误提示
      sheep.$helper.toast(errorMsg)
  }
}
```

## 6. 样式系统架构

### 6.1 设计系统

**色彩规范**:
```scss
// 主色调
$primary-color: #4a90e2;
$secondary-color: #5c6bc0;

// 功能色
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;

// 中性色
$text-primary: #1a1a1a;
$text-secondary: #666;
$background: #f5f5f5;
```

**圆角规范**:
```scss
$border-radius-large: 16rpx;  // 卡片
$border-radius-medium: 12rpx; // 组件
$border-radius-small: 8rpx;   // 输入框
```

**间距系统**:
```scss
$spacing-large: 30rpx;
$spacing-medium: 20rpx;  
$spacing-small: 12rpx;
```

### 6.2 统一样式架构

**页面容器统一样式**:
```scss
.content-container {
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 160rpx; // 底部按钮预留空间
  
  // 统一卡片样式
  :deep(.section),
  :deep(.fee-card),
  :deep(.team-info),
  :deep(.payment-section) {
    margin: 0 0 20rpx 0;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  }
  
  // 球员档案组件特殊处理
  :deep(.player-profile-container) {
    margin: 0 0 20rpx 0;
  }
}
```

**底部固定栏样式**:
```scss
.bottom-bar-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  border-top: 1rpx solid #e8e8e8;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}
```

### 6.3 响应式设计

**屏幕适配**:
- 使用 `rpx` 单位确保不同设备下的一致性
- 利用 `env(safe-area-inset-bottom)` 适配全面屏
- 通过媒体查询优化大屏设备体验

## 7. 前端技术实现架构

基于业务流程图，前端技术实现采用现代化的 Vue 3 + Composable 架构：

### 7.1 页面结构架构

```mermaid
graph LR
    subgraph "活动页面群"
        A[活动列表页<br/>list.vue]
        B[活动详情页<br/>detail.vue]
        C[确认订单页<br/>confirm-order.vue]
    end
    
    subgraph "支付页面群"
        P[支付页面<br/>pay/index.vue]
        R[支付结果页<br/>pay/result.vue]
    end
    
    subgraph "核心组件库"
        E[确认订单组件<br/>OrderXxx.vue]
        F[活动详情组件<br/>ActivityXxx.vue]
        G[好友组队组件<br/>FriendTeamRoom.vue]
    end
    
    subgraph "业务逻辑层"
        H[useConfirmOrder<br/>Composable]
        I[ActivityStore<br/>Pinia Store]
        J[活动API<br/>ActivityApi]
        K[支付系统<br/>pay.js]
    end
    
    A --> B
    B --> C
    C --> P
    P --> R
    
    B --> F
    C --> E
    B --> G
    C --> G
    
    C --> H
    P --> K
    H --> I
    H --> J
```

### 7.2 组件复用策略

**FriendTeamRoom 组件**是典型的复用设计：

```vue
<!-- 活动详情页使用 -->
<FriendTeamRoom 
  :roomData="activity.signupRelatedInfo?.friendTeamRoom" 
  mode="detail"
  @invite="handleInviteFriend" 
/>

<!-- 确认订单页使用 -->
<FriendTeamRoom 
  :roomData="friendGroupInfo" 
  mode="simple"
/>
```

**设计原则**：
- 通过 `mode` 属性控制展示详细程度
- 统一数据结构和交互逻辑
- 减少重复开发，提高维护效率

### 7.3 状态管理架构优化

**混合状态管理模式**：

```javascript
// 全局状态 (Pinia Store)
export const useActivityStore = defineStore('activity', {
  state: () => ({
    activityList: [],           // 活动列表
    currentActivityDetail: null, // 当前活动详情
    currentRoomInfo: null,      // 当前好友组信息
  })
});

// 页面状态 (Composable)
export default function useConfirmOrder(routeParams) {
  // 页面级响应式状态
  const loading = ref(true);
  const activity = ref(null);
  const feeInfo = reactive({});
  
  // 计算属性
  const isReady = computed(() => !loading.value && activity.value);
  
  // 业务方法
  const init = async () => { /* 初始化逻辑 */ };
  
  return { loading, activity, feeInfo, isReady, init };
}
```

### 7.4 API 交互设计

**关键 API 调用流程**：

1. **活动详情获取**：
   ```javascript
   // detail.vue
   const response = await ActivityApi.getDetail(activityId);
   ```

2. **订单结算**：
   ```javascript
   // useConfirmOrder.js
   const response = await ActivityApi.settlementRegistration({
     activityId: Number(routeParams.activityId),
     activityType: Number(routeParams.activityType),
     signupMode: Number(routeParams.signupMode),
     teamId: routeParams.teamId,
     roomId: routeParams.roomId
   });
   ```

3. **订单创建**：
   ```javascript
   // useConfirmOrder.js
   const response = await ActivityApi.createRegistration(orderData);
   ```

### 7.5 错误处理机制

**分层错误处理**：

```javascript
// API 层错误处理
const handleAPIError = (error) => {
  switch (error.code) {
    case 1_001_002_004: // 球员档案必填
      showProfileForm();
      break;
    case 1_001_001_001: // 重复报名
      redirectToExistingOrder();
      break;
    default:
      sheep.$helper.toast(error.message);
  }
};

// 页面层降级方案
const loadWithFallback = async () => {
  try {
    return await loadRealData();
  } catch (error) {
    console.warn('加载失败，使用Mock数据:', error);
    return loadMockData();
  }
};
```

### 7.6 性能优化策略

1. **智能价格重算**：只在优惠券/积分变更时触发
2. **组件按需加载**：使用 `defineAsyncComponent`
3. **状态管理优化**：合理使用 `computed` 和 `watch`
4. **缓存策略**：活动详情数据本地缓存

### 7.7 支付流程详细实现

**完整支付链路**：

```mermaid
sequenceDiagram
    participant User as 用户
    participant Order as 确认订单页<br/>confirm-order.vue
    participant PayIndex as 支付页面<br/>pay/index.vue
    participant PaySDK as 支付SDK<br/>pay.js
    participant PayResult as 支付结果页<br/>pay/result.vue
    participant API as 后端API

    Note over User,API: 1. 订单确认阶段
    User->>Order: 填写信息、选择优惠券
    Order->>API: 调用结算接口
    API-->>Order: 返回订单金额
    User->>Order: 点击"提交订单"
    Order->>API: 创建支付订单
    API-->>Order: 返回支付订单ID

    Note over User,API: 2. 支付页面阶段
    Order->>PayIndex: 跳转到支付页 (携带订单ID)
    PayIndex->>API: 获取支付订单详情
    API-->>PayIndex: 返回支付方式和订单信息
    PayIndex->>User: 展示支付方式选择

    Note over User,API: 3. 支付执行阶段
    User->>PayIndex: 选择支付方式
    PayIndex->>PaySDK: 调用对应支付方法
    
    alt 微信支付
        PaySDK->>PaySDK: wechatMiniProgramPay()
    else 支付宝支付
        PaySDK->>PaySDK: alipay()
    else 余额支付
        PaySDK->>PaySDK: walletPay()
    end
    
    PaySDK->>API: 调用支付接口
    API-->>PaySDK: 返回支付结果

    Note over User,API: 4. 支付结果阶段
    PaySDK->>PayResult: goPayResult() 跳转结果页
    PayResult->>API: 轮询支付状态
    API-->>PayResult: 返回最终支付状态
    
    alt 支付成功
        PayResult->>User: 显示成功页面 + 下一步操作
    else 支付失败
        PayResult->>User: 显示失败页面 + 重试按钮
    end
```

**支付SDK核心实现**：

```javascript
// pay.js 核心逻辑
export default class SheepPay {
  constructor(payment, orderType, id) {
    this.payment = payment      // 支付信息
    this.orderType = orderType  // 订单类型
    this.id = id               // 订单ID
  }
  
  async payAction() {
    // 根据支付渠道调用不同方法
    switch(this.payment.channel) {
      case 'wechat': return await this.wechatMiniProgramPay()
      case 'alipay': return await this.alipay()  
      case 'wallet': return await this.walletPay()
    }
  }
  
  payResult(resultType) {
    // 统一跳转到结果页
    return goPayResult(this.id, this.orderType, resultType)
  }
}

// 支付结果页整合逻辑
export function goPayResult(id, orderType, resultType) {
  const url = `/pages/pay/result?id=${id}&orderType=${orderType}&payResult=${resultType}`
  
  // 根据订单类型确定结果页展示内容
  if (orderType === 'activity') {
    // 活动订单：显示报名成功信息
    // 整合原 pay-success.vue 的逻辑
  }
  
  sheep.$router.go(url)
}
```

### 7.8 开发规范

**组件开发规范**：
```vue
<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue';

// 2. 定义 Props 和 Emits
const props = defineProps({
  data: { type: Object, required: true }
});
const emit = defineEmits(['update', 'submit']);

// 3. 响应式状态
const loading = ref(false);

// 4. 计算属性
const isValid = computed(() => { /* 计算逻辑 */ });

// 5. 方法定义
const handleSubmit = () => { /* 处理逻辑 */ };

// 6. 生命周期
onMounted(() => { /* 初始化 */ });
</script>
```

**命名规范**：
- 组件名：PascalCase (`OrderBottomBar`)
- 方法名：camelCase (`handleSubmit`)
- 常量名：SCREAMING_SNAKE_CASE (`ACTIVITY_TYPE`)
- CSS类名：kebab-case (`bottom-bar-wrapper`)

## 8. UI 设计规范

### 8.1 设计系统概述

活动页面UI设计遵循统一的设计系统，确保视觉一致性和用户体验的连贯性。设计系统基于原子设计方法论，从基础设计令牌到复杂组件层层构建。

**设计原则**：
- **一致性**：统一的视觉语言和交互模式
- **可访问性**：符合无障碍设计标准
- **响应式**：适配不同设备尺寸
- **易扩展**：支持后续功能模块的快速接入

### 8.2 色彩系统

```scss
// CSS变量定义 - 支持主题切换
:root {
  /* 主色调 */
  --primary-color: #4a90e2;
  --primary-hover: #3a7bd5;
  --primary-active: #2968b8;
  --primary-disabled: #a5c7ea;
  
  /* 辅助色 */
  --secondary-color: #5c6bc0;
  --accent-color: #f59520;
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --error-color: #FF3B30;
  
  /* 中性色 */
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-tertiary: #bdc3c7;
  --text-inverse: #ffffff;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f7fa;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 边框色 */
  --border-light: #e0e0e0;
  --border-medium: #bdbdbd;
  --border-dark: #757575;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, #f59520, #f7b500);
}
```

### 8.3 排版系统

```scss
// 字体定义
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

// 字号体系 (rpx单位，适配小程序)
$font-sizes: (
  'xxl': 48rpx,    // 页面主标题
  'xl': 36rpx,     // 重要信息标题
  'lg': 32rpx,     // 小节标题
  'md': 30rpx,     // 强调文本
  'base': 28rpx,   // 正文内容
  'sm': 26rpx,     // 辅助文本
  'xs': 24rpx,     // 说明文字
  'xxs': 20rpx     // 极小文字
);

// 行高体系
$line-heights: (
  'tight': 1.2,
  'normal': 1.4,
  'relaxed': 1.6,
  'loose': 1.8
);

// 字重体系
$font-weights: (
  'light': 300,
  'normal': 400,
  'medium': 500,
  'semibold': 600,
  'bold': 700
);

// 工具类生成
@each $size, $value in $font-sizes {
  .text-#{$size} {
    font-size: $value;
  }
}

@each $weight, $value in $font-weights {
  .font-#{$weight} {
    font-weight: $value;
  }
}
```

### 8.4 间距与布局

```scss
// 间距系统 (8的倍数设计)
$spacings: (
  'xs': 8rpx,
  'sm': 16rpx,
  'md': 24rpx,
  'lg': 32rpx,
  'xl': 40rpx,
  'xxl': 48rpx,
  'xxxl': 64rpx
);

// 圆角系统
$border-radius: (
  'sm': 4rpx,
  'md': 8rpx,
  'lg': 16rpx,
  'xl': 24rpx,
  'round': 50%
);

// 阴影系统
$shadows: (
  'sm': 0 2rpx 8rpx rgba(0, 0, 0, 0.1),
  'md': 0 4rpx 16rpx rgba(0, 0, 0, 0.12),
  'lg': 0 8rpx 32rpx rgba(0, 0, 0, 0.15),
  'xl': 0 16rpx 64rpx rgba(0, 0, 0, 0.2)
);

// 布局容器
.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.page-container {
  min-height: 100vh;
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  padding-bottom: calc(env(safe-area-inset-bottom) + 160rpx);
}
```

### 8.5 组件样式规范

**按钮组件样式**：
```scss
// 按钮基础样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: $font-family-base;
  font-weight: map-get($font-weights, 'medium');
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  text-decoration: none;
  cursor: pointer;
  
  // 尺寸变体
  &.btn-sm {
    height: 64rpx;
    padding: 0 var(--spacing-md);
    font-size: map-get($font-sizes, 'sm');
    border-radius: map-get($border-radius, 'md');
  }
  
  &.btn-md {
    height: 80rpx;
    padding: 0 var(--spacing-lg);
    font-size: map-get($font-sizes, 'base');
    border-radius: map-get($border-radius, 'lg');
  }
  
  &.btn-lg {
    height: 96rpx;
    padding: 0 var(--spacing-xl);
    font-size: map-get($font-sizes, 'md');
    border-radius: map-get($border-radius, 'xl');
  }
  
  // 主要按钮
  &.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    box-shadow: map-get($shadows, 'sm');
    
    &:hover {
      transform: translateY(-2rpx);
      box-shadow: map-get($shadows, 'md');
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:disabled {
      background: var(--primary-disabled);
      transform: none;
      box-shadow: none;
      cursor: not-allowed;
    }
  }
  
  // 次要按钮
  &.btn-secondary {
    background: var(--bg-primary);
    color: var(--primary-color);
    border-color: var(--primary-color);
    
    &:hover {
      background: var(--primary-color);
      color: var(--text-inverse);
    }
  }
  
  // 文字按钮
  &.btn-text {
    background: transparent;
    color: var(--primary-color);
    
    &:hover {
      background: rgba(74, 144, 226, 0.1);
    }
  }
}
```

**卡片组件样式**：
```scss
.card {
  background: var(--bg-primary);
  border-radius: map-get($border-radius, 'xl');
  box-shadow: map-get($shadows, 'sm');
  overflow: hidden;
  
  &.card-hover {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4rpx);
      box-shadow: map-get($shadows, 'md');
    }
  }
  
  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1rpx solid var(--border-light);
    
    .card-title {
      font-size: map-get($font-sizes, 'lg');
      font-weight: map-get($font-weights, 'semibold');
      color: var(--text-primary);
      margin: 0;
    }
    
    .card-subtitle {
      font-size: map-get($font-sizes, 'sm');
      color: var(--text-secondary);
      margin: 8rpx 0 0 0;
    }
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  .card-footer {
    padding: var(--spacing-lg);
    border-top: 1rpx solid var(--border-light);
    background: var(--bg-secondary);
  }
}
```

**表单组件样式**：
```scss
.form-group {
  margin-bottom: var(--spacing-lg);
  
  .form-label {
    display: block;
    font-size: map-get($font-sizes, 'sm');
    font-weight: map-get($font-weights, 'medium');
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    
    &.required::after {
      content: ' *';
      color: var(--error-color);
    }
  }
  
  .form-control {
    width: 100%;
    height: 80rpx;
    padding: 0 var(--spacing-md);
    font-size: map-get($font-sizes, 'base');
    color: var(--text-primary);
    background: var(--bg-primary);
    border: 2rpx solid var(--border-light);
    border-radius: map-get($border-radius, 'md');
    transition: all 0.3s ease;
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
    }
    
    &.error {
      border-color: var(--error-color);
      
      &:focus {
        box-shadow: 0 0 0 6rpx rgba(255, 59, 48, 0.1);
      }
    }
    
    &:disabled {
      background: var(--bg-secondary);
      color: var(--text-tertiary);
      cursor: not-allowed;
    }
  }
  
  .form-error {
    display: block;
    font-size: map-get($font-sizes, 'xs');
    color: var(--error-color);
    margin-top: var(--spacing-xs);
  }
}
```

### 8.6 页面布局模板

**活动详情页布局**：
```scss
.activity-detail-layout {
  .hero-section {
    position: relative;
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: var(--spacing-xl) var(--spacing-lg);
    margin: calc(-1 * var(--spacing-md));
    margin-bottom: var(--spacing-lg);
    
    .activity-image {
      width: 100%;
      height: 300rpx;
      object-fit: cover;
      border-radius: map-get($border-radius, 'lg');
      margin-bottom: var(--spacing-md);
    }
    
    .activity-title {
      font-size: map-get($font-sizes, 'xl');
      font-weight: map-get($font-weights, 'bold');
      margin-bottom: var(--spacing-xs);
    }
    
    .activity-meta {
      font-size: map-get($font-sizes, 'sm');
      opacity: 0.9;
    }
  }
  
  .content-sections {
    .section {
      background: var(--bg-primary);
      border-radius: map-get($border-radius, 'xl');
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-md);
      box-shadow: map-get($shadows, 'sm');
      
      .section-title {
        font-size: map-get($font-sizes, 'lg');
        font-weight: map-get($font-weights, 'semibold');
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 2rpx solid var(--border-light);
      }
    }
  }
  
  .bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    padding: var(--spacing-md);
    padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid var(--border-light);
    
    .action-buttons {
      display: flex;
      gap: var(--spacing-md);
      
      .btn {
        flex: 1;
      }
    }
  }
}
```

### 8.7 状态样式

```scss
// 加载状态
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxxl);
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid var(--border-light);
    border-top: 4rpx solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
  }
  
  .loading-text {
    font-size: map-get($font-sizes, 'sm');
    color: var(--text-secondary);
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: var(--spacing-xxxl) var(--spacing-lg);
  
  .empty-icon {
    font-size: 120rpx;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-lg);
  }
  
  .empty-title {
    font-size: map-get($font-sizes, 'lg');
    font-weight: map-get($font-weights, 'medium');
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
  }
  
  .empty-description {
    font-size: map-get($font-sizes, 'sm');
    color: var(--text-tertiary);
    line-height: map-get($line-heights, 'relaxed');
  }
}

// 错误状态
.error-state {
  text-align: center;
  padding: var(--spacing-xxxl) var(--spacing-lg);
  
  .error-icon {
    font-size: 120rpx;
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
  }
  
  .error-title {
    font-size: map-get($font-sizes, 'lg');
    font-weight: map-get($font-weights, 'medium');
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  .error-message {
    font-size: map-get($font-sizes, 'sm');
    color: var(--text-secondary);
    line-height: map-get($line-heights, 'relaxed');
    margin-bottom: var(--spacing-lg);
  }
}

// 活动状态标签
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: map-get($border-radius, 'round');
  font-size: map-get($font-sizes, 'xs');
  font-weight: map-get($font-weights, 'medium');
  
  &.status-recruiting {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
  }
  
  &.status-full {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
  }
  
  &.status-closed {
    background: rgba(158, 158, 158, 0.1);
    color: var(--text-tertiary);
  }
  
  &.status-ongoing {
    background: rgba(74, 144, 226, 0.1);
    color: var(--primary-color);
  }
}
```

### 8.8 动画与过渡

```scss
// 缓动函数
$easings: (
  'ease-out': cubic-bezier(0.25, 0.46, 0.45, 0.94),
  'ease-in-out': cubic-bezier(0.4, 0, 0.2, 1),
  'ease-bounce': cubic-bezier(0.68, -0.55, 0.265, 1.55)
);

// 持续时间
$durations: (
  'fast': 200ms,
  'normal': 300ms,
  'slow': 500ms
);

// 通用过渡类
.transition-all {
  transition: all map-get($durations, 'normal') map-get($easings, 'ease-out');
}

.transition-opacity {
  transition: opacity map-get($durations, 'normal') map-get($easings, 'ease-out');
}

.transition-transform {
  transition: transform map-get($durations, 'normal') map-get($easings, 'ease-out');
}

// 页面进入动画
.page-enter {
  animation: pageSlideIn map-get($durations, 'normal') map-get($easings, 'ease-out');
}

@keyframes pageSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片悬浮效果
.card-float {
  transition: all map-get($durations, 'normal') map-get($easings, 'ease-out');
  
  &:hover {
    transform: translateY(-8rpx);
  }
}

// 按钮点击效果
.btn-press {
  transition: all map-get($durations, 'fast') map-get($easings, 'ease-out');
  
  &:active {
    transform: scale(0.98);
  }
}

// 脉动动画（用于重要提示）
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 旋转动画（加载状态）
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
```

这些UI设计规范确保了活动报名系统的视觉一致性和良好的用户体验，同时为开发人员提供了清晰的实现指导。

## 9. 架构总结

### 9.1 技术架构优势

- ✅ **组件化设计**: 职责清晰，高度复用
- ✅ **Composable 模式**: 业务逻辑内聚，易于维护和测试
- ✅ **统一页面架构**: 单一页面支持多活动类型，降低开发成本
- ✅ **智能优化**: 减少网络请求，提升性能体验
- ✅ **完善错误处理**: 降级机制保障系统稳定性

### 9.2 核心设计成果

**统一入口设计**:
- `detail.vue`: 所有活动类型的统一详情页
- `confirm-order.vue`: 统一订单确认页，根据参数动态渲染

**用户体验优化**:
- 支付成功后立即分队，给用户即时反馈
- 实时状态更新，保持用户对进度的掌控感
- 分队情况透明展示，提升参与体验

该架构为活动业务的快速扩展和用户体验的持续提升提供了坚实的技术基础。

### 7.9 业务逻辑实现要点

#### 7.9.1 统一页面架构实现

**活动详情页统一架构**：
```vue
<!-- detail.vue - 统一活动详情页 -->
<template>
  <view class="activity-detail-page">
    <!-- 通用组件 -->
    <ActivityHeader />
    <SignupDeadlineCard />
    
    <!-- 基于活动类型的条件渲染 -->
    <template v-if="activity.type === ACTIVITY_TYPE.RANKING_MATCH">
      <!-- 排位赛特有组件 -->
      <InfoSection title="比赛信息" />
      <FriendTeamRoom v-if="userStatus.isInFriendGroup" mode="detail" />
      <SignupOptions v-if="!userStatus.isRegistered" />
      <ActivitySignupProgress />
    </template>
    
    <template v-else-if="activity.type === ACTIVITY_TYPE.FRIENDLY_MATCH">
      <!-- 友谊赛特有组件 -->
      <InfoSection title="比赛信息" />
      <FriendlyMatchSignupProgress />
    </template>
    
    <template v-else-if="activity.type === ACTIVITY_TYPE.LEAGUE">
      <!-- 联赛特有组件 -->
      <LeagueInfoSection />
      <LeagueFormatSection />
      <LeagueTeamProgress />
    </template>
    
    <!-- 通用组件 -->
    <ActivityFeeDetails />
    <ActivityServiceDetails />
    <ActivitySignupNotice />
    
    <!-- 底部操作栏 -->
    <BottomActionBar />
  </view>
</template>

<script setup>
import { ACTIVITY_TYPE } from '@/constants'

const props = defineProps({
  activityId: [String, Number]
})

// 使用统一的业务逻辑
const { activity, userStatus, handleSignup } = useActivityDetail(props.activityId)
</script>
```

**订单确认页统一架构**：
```vue
<!-- confirm-order.vue - 统一订单确认页 -->
<template>
  <view class="confirm-order-page">
    <OrderActivityInfoCard :activity="activity" />
    
    <!-- 基于报名模式的条件渲染 -->
    <template v-if="signupMode === 'individual'">
      <!-- 个人报名内容 -->
      <OrderNewUserProfileForm v-if="isNewUser" />
      <OrderUserInfoCard v-else />
    </template>
    
    <template v-else-if="signupMode === 'friend_group'">
      <!-- 好友组队内容 -->
      <FriendTeamRoom :roomData="friendGroupInfo" mode="simple" />
    </template>
    
    <template v-else-if="signupMode === 'team'">
      <!-- 球队报名内容 -->
      <OrderTeamInfo :teamData="teamInfo" />
      <OrderPaymentSection />
    </template>
    
    <!-- 通用组件 -->
    <OrderFeeDetailsCard />
    <OrderAgreementSection />
    <OrderBottomBar @submit="handleSubmitOrder" />
  </view>
</template>

<script setup>
// 路由参数获取
const route = useRoute()
const { activityId, activityType, signupMode, isNewUser, teamId, roomId } = route.query

// 使用统一的业务逻辑
const orderLogic = useConfirmOrder({
  activityId, activityType, signupMode, isNewUser, teamId, roomId
})

const { 
  activity, 
  userInfo, 
  teamInfo, 
  friendGroupInfo, 
  feeInfo, 
  handleSubmitOrder 
} = orderLogic
</script>
```

#### 7.9.2 立即分队机制实现

**支付成功后分队处理**：
```javascript
// 支付成功回调处理
async function onPaymentSuccess(paymentResult) {
  try {
    // 1. 更新报名状态
    await updateRegistrationStatus(paymentResult.orderId)
    
    // 2. 立即分队处理（核心用户体验优化）
    const teamAssignment = await assignTeamImmediately({
      userId: sheep.$store('user').userInfo.id,
      activityId: activity.value.id,
      activityType: activity.value.type,
      signupMode: signupMode.value,
      orderInfo: paymentResult.orderInfo
    })
    
    // 3. 显示分队结果，给用户即时反馈
    showTeamAssignmentResult(teamAssignment)
    
    // 4. 检查是否达标，决定是否正式组局
    await checkGroupingCapacity(activity.value.id)
    
    // 5. 跳转到活动详情页，显示最新状态
    sheep.$router.go('/pages/activity/detail', {
      id: activity.value.id,
      showAssignment: true
    })
  } catch (error) {
    console.error('支付成功后处理失败:', error)
    sheep.$helper.toast('报名处理异常，请联系客服')
  }
}

// 立即分队API调用
async function assignTeamImmediately(params) {
  const response = await sheep.$api.activity.assignTeamImmediately(params)
  
  if (response.code === 200) {
    return response.data
  } else {
    throw new Error(response.message || '分队处理失败')
  }
}
```

#### 7.9.3 分队展示策略实现

**排位赛分队状态展示**：
```javascript
// 排位赛分队数据结构
const rankingTeamAssignment = {
  team: 'home',           // 'home' | 'away'
  position: 1,            // 队内位置序号
  waitingNumber: 0,       // 候补排序（0表示正选）
  teamMemberCount: 3,     // 当前队伍人数
  needMemberCount: 2,     // 还需人数
  status: 'confirmed',    // 'confirmed' | 'waiting' | 'substitute'
  estimatedStartTime: '2024-01-15 19:00' // 预计开赛时间
}

// 分队状态显示组件
function useTeamAssignmentDisplay(assignment) {
  const displayText = computed(() => {
    if (assignment.waitingNumber === 0) {
      return `您已分配到${assignment.team === 'home' ? '主队' : '客队'}，当前${assignment.teamMemberCount}人，还需${assignment.needMemberCount}人`
    } else {
      return `您在候补队列第${assignment.waitingNumber}位，有人退出时将自动递补`
    }
  })
  
  const statusColor = computed(() => {
    switch (assignment.status) {
      case 'confirmed': return '#4CAF50'
      case 'waiting': return '#FFC107'
      case 'substitute': return '#FF9800'
      default: return '#9E9E9E'
    }
  })
  
  return { displayText, statusColor }
}
```

**友谊赛球队状态展示**：
```javascript
// 友谊赛球队数据结构
const friendlyTeamStatus = {
  teamId: 'team_123',
  teamName: '威震天队',
  registeredTeamCount: 4,  // 已报名球队数
  requiredTeamCount: 8,    // 需要球队数
  matchScheduled: false,   // 是否已安排对战
  opponentTeam: null,      // 对手球队信息
  matchTime: null         // 比赛时间
}

// 友谊赛状态显示
function useFriendlyTeamDisplay(teamStatus) {
  const statusText = computed(() => {
    if (teamStatus.matchScheduled) {
      return `对战安排已确定：VS ${teamStatus.opponentTeam.name}`
    } else {
      return `您的球队已报名，当前${teamStatus.registeredTeamCount}/${teamStatus.requiredTeamCount}支球队`
    }
  })
  
  const progressPercent = computed(() => {
    return (teamStatus.registeredTeamCount / teamStatus.requiredTeamCount) * 100
  })
  
  return { statusText, progressPercent }
}
```

**联赛状态展示**：
```javascript
// 联赛数据结构
const leagueStatus = {
  leaguePosition: 5,       // 联赛排名
  totalTeams: 16,          // 总参赛队伍
  bracketGenerated: false, // 对阵表是否生成
  groupStage: 'A',         // 分组信息
  nextMatchTime: null,     // 下一场比赛时间
  currentRound: 1          // 当前轮次
}

// 联赛状态显示
function useLeagueDisplay(leagueStatus) {
  const statusText = computed(() => {
    if (leagueStatus.bracketGenerated) {
      return `联赛${leagueStatus.groupStage}组，当前第${leagueStatus.currentRound}轮`
    } else {
      return `成功加入联赛，当前${leagueStatus.totalTeams}支球队，对阵表生成中`
    }
  })
  
  return { statusText }
}
```

#### 7.9.4 统一状态管理实现

**useConfirmOrder Composable 完整实现**：
```javascript
// composables/useConfirmOrder.js
export default function useConfirmOrder(routeParams) {
  const { activityId, activityType, signupMode, isNewUser, teamId, roomId } = routeParams
  
  // 响应式状态
  const loading = ref(true)
  const activity = ref(null)
  const userInfo = ref(null)
  const teamInfo = ref(null)
  const friendGroupInfo = ref(null)
  const feeInfo = ref(null)
  const agreementChecked = ref(false)
  
  // 根据参数动态处理不同场景
  const renderContent = computed(() => {
    if (signupMode === 'individual') return 'individual'
    if (signupMode === 'friend_group') return 'friend_group'
    if (signupMode === 'team') return 'team'
    return 'individual'
  })
  
  // 费用计算
  const totalFee = computed(() => {
    if (!feeInfo.value) return 0
    
    let total = feeInfo.value.baseFee
    if (feeInfo.value.discountAmount) {
      total -= feeInfo.value.discountAmount
    }
    if (feeInfo.value.pointsUsed) {
      total -= feeInfo.value.pointsUsed / 100 // 积分抵扣
    }
    
    return Math.max(total, 0)
  })
  
  // 数据加载
  async function loadOrderData() {
    try {
      loading.value = true
      
      // 并行加载数据
      const [activityData, userInfoData, feeData] = await Promise.all([
        sheep.$api.activity.getDetail(activityId),
        isNewUser ? null : sheep.$api.user.getUserInfo(),
        sheep.$api.activity.calculateFee({
          activityId,
          signupMode,
          teamId,
          roomId
        })
      ])
      
      activity.value = activityData.data
      userInfo.value = userInfoData?.data || null
      feeInfo.value = feeData.data
      
      // 根据模式加载额外数据
      if (signupMode === 'team' && teamId) {
        const teamData = await sheep.$api.team.getDetail(teamId)
        teamInfo.value = teamData.data
      }
      
      if (signupMode === 'friend_group' && roomId) {
        const roomData = await sheep.$api.friendGroup.getRoomDetail(roomId)
        friendGroupInfo.value = roomData.data
      }
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      sheep.$helper.toast('数据加载失败，请重试')
    } finally {
      loading.value = false
    }
  }
  
  // 提交订单
  async function handleSubmitOrder(formData) {
    if (!agreementChecked.value) {
      sheep.$helper.toast('请先阅读并同意相关协议')
      return
    }
    
    try {
      const orderData = {
        activityId: activity.value.id,
        activityType: activity.value.type,
        signupMode,
        ...formData,
        feeInfo: feeInfo.value
      }
      
      // 根据不同模式添加特定参数
      if (signupMode === 'team') {
        orderData.teamId = teamId
      } else if (signupMode === 'friend_group') {
        orderData.roomId = roomId
      }
      
      const response = await sheep.$api.activity.createOrder(orderData)
      
      if (response.code === 200) {
        // 跳转到支付页
        sheep.$router.go('/pages/pay/index', {
          id: response.data.orderId,
          orderType: 'activity'
        })
      } else {
        throw new Error(response.message || '订单创建失败')
      }
      
    } catch (error) {
      console.error('提交订单失败:', error)
      sheep.$helper.toast(error.message || '提交失败，请重试')
    }
  }
  
  // 初始化加载
  onMounted(() => {
    loadOrderData()
  })
  
  return {
    // 状态
    loading,
    activity,
    userInfo,
    teamInfo,
    friendGroupInfo,
    feeInfo,
    agreementChecked,
    
    // 计算属性
    renderContent,
    totalFee,
    
    // 方法
    handleSubmitOrder,
    loadOrderData
  }
}
```

这些业务逻辑实现确保了：
- **统一的页面架构**：通过条件渲染实现多活动类型支持
- **立即分队体验**：支付成功后即时处理，提升用户满意度
- **清晰的状态管理**：Composable 模式封装业务逻辑
- **完整的错误处理**：保障系统稳定性和用户体验