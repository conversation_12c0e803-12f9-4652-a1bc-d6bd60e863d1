<template>
  <view class="team-group-page">
    <!-- 页面加载状态 -->
    <view class="loading-container" v-if="loading">
      <uni-load-more status="loading" />
    </view>
    
    <!-- 页面内容 -->
    <template v-else-if="roomInfo">
      <view class="room-header">
        <image class="header-bg" src="/static/images/team-group-bg.jpg" mode="aspectFill"></image>
        <view class="header-content">
          <view class="room-title">好友组队房间</view>
          <view class="room-status" :class="[`status-${roomInfo.status}`]">
            <view class="status-dot"></view>
            <text>{{ getStatusText() }}</text>
          </view>
        </view>
      </view>
      
      <view class="room-content">
        <!-- 房间信息 -->
        <view class="room-info-card">
          <view class="info-item">
            <text class="item-label">房间编号:</text>
            <text class="item-value">{{ roomInfo.id }}</text>
          </view>
          <view class="info-item">
            <text class="item-label">活动名称:</text>
            <text class="item-value">{{ activityTitle }}</text>
            <text class="item-action" @click="navigateToActivity">查看</text>
          </view>
          <view class="info-item">
            <text class="item-label">成员上限:</text>
            <text class="item-value">{{ roomInfo.maxMembers }}人</text>
          </view>
          <view class="info-item">
            <text class="item-label">当前成员:</text>
            <text class="item-value">{{ roomInfo.currentMembers }}人</text>
          </view>
          <view class="info-item" v-if="roomInfo.roomCode">
            <text class="item-label">邀请码:</text>
            <text class="item-value">{{ roomInfo.roomCode }}</text>
            <text class="item-action" @click="copyRoomCode">复制</text>
          </view>
        </view>
        
        <!-- 邀请按钮 -->
        <view class="invite-section">
          <button class="invite-button" @click="showShareModal">
            <uni-icons type="redo" size="18" color="#ffffff"></uni-icons>
            <text>邀请好友加入</text>
          </button>
          <text class="invite-tip">邀请好友加入，一起组队报名吧！</text>
        </view>
        
        <!-- 成员列表 -->
        <view class="members-card">
          <view class="card-title">
            <text>成员列表</text>
            <text class="member-count">{{ roomInfo.currentMembers }}/{{ roomInfo.maxMembers }}</text>
          </view>
          
          <view class="member-list">
            <!-- 成员项 -->
            <view 
              class="member-item" 
              v-for="(member, index) in roomInfo.members" 
              :key="index"
            >
              <view class="member-avatar">
                <image :src="member.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                <view class="owner-badge" v-if="member.isOwner">房主</view>
              </view>
              <view class="member-info">
                <text class="member-name">{{ member.nickname || '未知用户' }}</text>
                <text class="member-status" :class="{ 'paid': member.paymentStatus === 1 }">
                  {{ member.paymentStatus === 1 ? '已支付' : '未支付' }}
                </text>
              </view>
              <view class="member-action">
                <button 
                  class="pay-button" 
                  v-if="isCurrentUser(member.userId) && member.paymentStatus === 0"
                  @click="handlePay"
                >去支付</button>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 操作区域 -->
        <view class="action-section">
          <button 
            class="action-button refresh" 
            @click="fetchRoomDetail"
          >
            <uni-icons type="refresh" size="16" color="#2196F3"></uni-icons>
            <text>刷新</text>
          </button>
          <button 
            class="action-button dismiss" 
            v-if="isOwner"
            @click="confirmDismiss"
          >
            <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
            <text>解散房间</text>
          </button>
          <button 
            class="action-button leave" 
            v-else
            @click="confirmLeave"
          >
            <uni-icons type="closeempty" size="16" color="#FF9800"></uni-icons>
            <text>退出房间</text>
          </button>
        </view>
      </view>
    </template>
    
    <!-- 加载失败 -->
    <view class="error-container" v-else>
      <text>加载失败，房间不存在或已解散</text>
      <button @click="navigateBack">返回</button>
    </view>
    
    <!-- 自动刷新定时器 -->
    <view class="auto-refresh-tip" v-if="autoRefreshEnabled">
      <text>{{ refreshCountdown }}秒后自动刷新</text>
      <text class="cancel-refresh" @click="cancelAutoRefresh">取消</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useActivityStore } from '@/sheep/store/activity';

// 页面参数
const props = defineProps({
  roomId: {
    type: [Number, String],
    default: ''
  }
});

// 状态管理
const activityStore = useActivityStore();
const roomInfo = computed(() => activityStore.currentRoomInfo);

// 本地状态
const loading = ref(true);
const activityTitle = ref(''); // 实际使用中可以从后端获取或本地存储
const userId = ref(0); // 当前用户ID，实际使用中从用户store获取
const refreshTimer = ref(null);
const autoRefreshEnabled = ref(false);
const refreshCountdown = ref(30);

// 计算属性
const isOwner = computed(() => {
  if (!roomInfo.value || !userId.value) return false;
  return roomInfo.value.ownerUserId === userId.value;
});

// 生命周期钩子
onMounted(async () => {
  // 获取用户信息
  getUserInfo();
  
  if (props.roomId) {
    await fetchRoomDetail();
    
    // 启动自动刷新
    startAutoRefresh();
  }
});

onUnmounted(() => {
  // 清理定时器
  clearRefreshTimer();
});

// 方法
// 获取用户信息
const getUserInfo = () => {
  // TODO: 实际使用时从用户store获取
  userId.value = 123; // 模拟用户ID
};

// 获取房间详情
const fetchRoomDetail = async () => {
  loading.value = true;
  try {
    const roomData = await activityStore.fetchRoomDetail(props.roomId);
    if (roomData) {
      // 假设活动标题从本地存储或者其他地方获取
      activityTitle.value = '周三晚间排位赛'; // 示例标题
    }
  } catch (error) {
    console.error('获取房间详情失败', error);
    uni.showToast({
      title: '获取房间信息失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 获取状态文本
const getStatusText = () => {
  if (!roomInfo.value) return '未知状态';
  
  switch (roomInfo.value.status) {
    case 1:
      return '组队中';
    case 2:
      return '已满员';
    case 3:
      return '已解散';
    default:
      return '未知状态';
  }
};

// 检查是否为当前用户
const isCurrentUser = (memberId) => {
  return userId.value === memberId;
};

// 处理支付
const handlePay = () => {
  // TODO: 跳转到支付页面
  uni.navigateTo({
    url: `/pages/order/confirm?activityId=${roomInfo.value.activityId}&signupMode=2&roomId=${props.roomId}`
  });
};

// 解散房间确认
const confirmDismiss = () => {
  uni.showModal({
    title: '解散房间',
    content: '确定要解散房间吗？解散后所有成员将无法加入此房间。',
    confirmText: '确定解散',
    confirmColor: '#FF4D4F',
    success: async (res) => {
      if (res.confirm) {
        await dismissRoom();
      }
    }
  });
};

// 解散房间
const dismissRoom = async () => {
  try {
    await activityStore.dismissRoom(props.roomId);
    uni.showToast({
      title: '房间已解散',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      navigateBack();
    }, 1500);
  } catch (error) {
    console.error('解散房间失败', error);
    uni.showToast({
      title: '解散房间失败',
      icon: 'none'
    });
  }
};

// 退出房间确认
const confirmLeave = () => {
  uni.showModal({
    title: '退出房间',
    content: '确定要退出此房间吗？',
    confirmText: '确定退出',
    confirmColor: '#FF9800',
    success: async (res) => {
      if (res.confirm) {
        await leaveRoom();
      }
    }
  });
};

// 退出房间
const leaveRoom = async () => {
  try {
    // TODO: 实现退出房间的API调用
    uni.showToast({
      title: '已退出房间',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      navigateBack();
    }, 1500);
  } catch (error) {
    console.error('退出房间失败', error);
    uni.showToast({
      title: '退出房间失败',
      icon: 'none'
    });
  }
};

// 复制房间邀请码
const copyRoomCode = () => {
  if (!roomInfo.value || !roomInfo.value.roomCode) return;
  
  uni.setClipboardData({
    data: roomInfo.value.roomCode,
    success: () => {
      uni.showToast({
        title: '邀请码已复制',
        icon: 'success'
      });
    }
  });
};

// 显示分享模态框
const showShareModal = () => {
  // TODO: 显示分享模态框
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

// 导航到活动详情页
const navigateToActivity = () => {
  if (!roomInfo.value || !roomInfo.value.activityId) return;
  
  uni.navigateTo({
    url: `/pages/activity/detail?id=${roomInfo.value.activityId}`
  });
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 启动自动刷新
const startAutoRefresh = () => {
  autoRefreshEnabled.value = true;
  refreshCountdown.value = 30;
  
  // 清理已有定时器
  clearRefreshTimer();
  
  // 倒计时定时器
  refreshTimer.value = setInterval(() => {
    refreshCountdown.value--;
    
    if (refreshCountdown.value <= 0) {
      // 刷新数据
      fetchRoomDetail();
      // 重置倒计时
      refreshCountdown.value = 30;
    }
  }, 1000);
};

// 取消自动刷新
const cancelAutoRefresh = () => {
  autoRefreshEnabled.value = false;
  clearRefreshTimer();
};

// 清理刷新定时器
const clearRefreshTimer = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
};
</script>

<style lang="scss" scoped>
.team-group-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 100rpx 0;
    
    button {
      margin-top: 30rpx;
      font-size: 28rpx;
      background-color: #2196F3;
      color: white;
      padding: 10rpx 40rpx;
      border-radius: 30rpx;
    }
  }
  
  .room-header {
    position: relative;
    height: 220rpx;
    overflow: hidden;
    
    .header-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      filter: brightness(0.6);
    }
    
    .header-content {
      position: relative;
      z-index: 1;
      height: 100%;
      padding: 0 30rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .room-title {
        color: #ffffff;
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
      
      .room-status {
        display: inline-flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 6rpx 20rpx;
        border-radius: 30rpx;
        max-width: fit-content;
        
        .status-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          margin-right: 10rpx;
        }
        
        text {
          font-size: 24rpx;
          color: white;
        }
        
        &.status-1 {
          .status-dot {
            background-color: #4CAF50;
          }
        }
        
        &.status-2 {
          .status-dot {
            background-color: #FF9800;
          }
        }
        
        &.status-3 {
          .status-dot {
            background-color: #999;
          }
        }
      }
    }
  }
  
  .room-content {
    padding: 20rpx;
    
    .room-info-card {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-label {
          font-size: 28rpx;
          color: #666;
          width: 160rpx;
        }
        
        .item-value {
          font-size: 28rpx;
          color: #333;
          flex: 1;
        }
        
        .item-action {
          font-size: 26rpx;
          color: #2196F3;
          padding: 6rpx 16rpx;
        }
      }
    }
    
    .invite-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 40rpx 0;
      
      .invite-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #2196F3;
        color: #fff;
        font-size: 30rpx;
        padding: 20rpx 60rpx;
        border-radius: 40rpx;
        margin-bottom: 16rpx;
        
        .uni-icons {
          margin-right: 10rpx;
        }
      }
      
      .invite-tip {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .members-card {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      
      .card-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30rpx;
        
        text {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
        }
        
        .member-count {
          font-size: 28rpx;
          color: #999;
          font-weight: normal;
        }
      }
      
      .member-list {
        .member-item {
          display: flex;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1px solid #f5f5f5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .member-avatar {
            position: relative;
            margin-right: 20rpx;
            
            image {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
            }
            
            .owner-badge {
              position: absolute;
              bottom: -6rpx;
              right: -6rpx;
              background-color: #FF9800;
              color: white;
              font-size: 20rpx;
              padding: 2rpx 10rpx;
              border-radius: 10rpx;
            }
          }
          
          .member-info {
            flex: 1;
            
            .member-name {
              font-size: 28rpx;
              color: #333;
              margin-bottom: 6rpx;
            }
            
            .member-status {
              font-size: 24rpx;
              color: #FF9800;
              
              &.paid {
                color: #4CAF50;
              }
            }
          }
          
          .member-action {
            .pay-button {
              font-size: 26rpx;
              color: white;
              background-color: #FF4D4F;
              padding: 6rpx 20rpx;
              border-radius: 30rpx;
            }
          }
        }
      }
    }
    
    .action-section {
      display: flex;
      justify-content: center;
      margin: 40rpx 0;
      
      .action-button {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        padding: 16rpx 40rpx;
        border-radius: 30rpx;
        margin: 0 20rpx;
        
        .uni-icons {
          margin-right: 10rpx;
        }
        
        &.refresh {
          background-color: #f8f8f8;
          color: #2196F3;
        }
        
        &.dismiss {
          background-color: #FFF1F0;
          color: #FF4D4F;
        }
        
        &.leave {
          background-color: #FFF8E6;
          color: #FF9800;
        }
      }
    }
  }
  
  .auto-refresh-tip {
    position: fixed;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 24rpx;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    
    .cancel-refresh {
      margin-left: 20rpx;
      color: #2196F3;
    }
  }
}
</style> 