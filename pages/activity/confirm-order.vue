<template>
  <s-layout navbar="normal" :showMenu="false" title="确认订单" :bgStyle="bgStyle">
    <!-- 内容区域 -->
    <view class="content-container" v-if="isReady">
      <!-- 活动信息 -->
      <OrderActivityInfoCard :activity="activity" />

      <!-- 条件渲染：新用户球员档案表单 -->
      <OrderNewUserProfileForm v-if="playerProfileInfo.isNewUser" v-model="newPlayerProfile" />

      <!-- 条件渲染：排位赛相关内容 -->
      <template v-if="Number(props.activityType) === ACTIVITY_TYPE.RANKING_MATCH">
        <!-- 好友组队房间信息（仅在好友组队报名模式下显示） -->
        <FriendTeamRoom 
          v-if="Number(props.signupMode) === SIGNUP_MODE.FRIEND_GROUP && friendGroupInfo" 
          :roomData="friendGroupInfo" 
          mode="simple" 
        />
      </template>

      <!-- 条件渲染：友谊赛相关内容 -->
      <template v-if="Number(props.activityType) === ACTIVITY_TYPE.FRIENDLY_MATCH">
        <!-- 球队信息 -->
        <OrderTeamInfo v-if="teamInfo" :teamInfo="teamInfo" />
        <view v-else class="no-team-info">
          <view class="section">
            <view class="section-title">报名球队</view>
            <view class="empty-team-card">
              <uni-icons type="info" size="24" color="#999"></uni-icons>
              <text class="empty-text">未选择球队，请返回重新选择</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 条件渲染：联赛相关内容 -->
      <template v-if="Number(props.activityType) === ACTIVITY_TYPE.LEAGUE">
        <!-- 球队信息 -->
        <OrderTeamInfo v-if="teamInfo" :teamInfo="teamInfo" />
        <view v-else class="no-team-info">
          <view class="section">
            <view class="section-title">报名球队</view>
            <view class="empty-team-card">
              <uni-icons type="info" size="24" color="#999"></uni-icons>
              <text class="empty-text">未选择球队，请返回重新选择</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 统一费用明细与优惠 -->
      <!-- 支付方式选择 -->
      <OrderPaymentSection v-if="shouldShowPaymentOptions" :activityType="Number(props.activityType)"
        :signupMode="Number(props.signupMode)" :teamPaymentMethod="teamPaymentMethod" 
        :activityFeeInfo="{ 
          ...activityFeeInfo,
          friendlyTeamFee: activityFeeInfo?.friendlyTeamFee || 0,
          friendlyAAFee: activityFeeInfo?.friendlyAAFee || 0
        }"
        :estimatedFeePerPlayer="feeInfo.estimatedFeePerPlayer" :teamMembersCount="teamInfo?.members?.length || 5"
        @update:teamPaymentMethod="selectTeamPaymentMethod" />

      <!-- 费用明细与优惠 -->
      <OrderFeeDetailsCard :feeInfo="feeInfo" :activityFeeInfo="activityFeeInfo" :selectedCouponId="selectedCouponId"
        :isUsingPoints="isUsingPoints" :availableCoupons="couponInfo" :userPointsInfo="userPointsInfo"
        :paymentType="getPaymentTypeForFeeExplanation()" :feeDescriptionText="feeDescriptionText"
        @select-coupon="handleCouponSelect" @update:selectedCouponId="selectedCouponId = $event"
        @toggle-points="handleTogglePoints" />

      <!-- 底部占位 -->
      <view class="bottom-spacer"></view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-bar-wrapper" v-if="isReady">
      <!-- 整合的协议和提交按钮 -->
      <OrderBottomBarWithAgreement 
        :finalAmount="feeInfo.payAmount / 100" 
        :isSubmitting="submitting"
        :agreementChecked="agreementChecked" 
        :canSubmit="canSubmit" 
        @submit="handleSubmit"
        @agreementChange="handleAgreementChange"
        @openAgreement="openAgreementModal" 
      />
    </view>

    <!-- 风险协议弹框 -->
    <SimpleRiskModal :show="showRiskNotification" @confirm="onReadRiskNotification" @close="closeRiskNotification" />
  </s-layout>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ACTIVITY_TYPE, SIGNUP_MODE } from '@/sheep/store/activity';
import useConfirmOrder from '@/sheep/hooks/useConfirmOrder';

// 导入活动信息卡片
import OrderActivityInfoCard from './components/order/OrderActivityInfoCard.vue';

// 导入底部操作区域
import OrderBottomBarWithAgreement from './components/order/OrderBottomBarWithAgreement.vue';

//订单相关
import OrderPaymentSection from './components/order/OrderPaymentSection.vue';
import OrderFeeDetailsCard from './components/order/OrderFeeDetailsCard.vue';

// 导入排位赛专用组件
import FriendTeamRoom from './components/detail/FriendTeamRoom.vue';

// 导入通用组件
import OrderNewUserProfileForm from './components/order/OrderNewUserProfileFormSimple.vue';
import SimpleRiskModal from './components/detail/SimpleRiskModal.vue';

// 导入球队信息组件
import OrderTeamInfo from './components/order/OrderTeamInfo.vue';

// 获取页面参数
const props = defineProps({
  activityId: {
    type: [String, Number],
    required: true
  },
  activityType: {
    type: [String, Number],
    required: true
  },
  signupMode: {
    type: [String, Number],
    required: true
  },
  isNewUser: {
    type: [String, Boolean],
    default: false
  },
  teamId: {
    type: [String, Number],
    default: null
  },
  teamName: {
    type: String,
    default: ''
  },
  teamLogo: {
    type: String,
    default: ''
  },
  roomId: {
    type: [String, Number],
    default: null
  },
  invite_code: {
    type: String,
    default: ''
  }
});

// 页面样式
const bgStyle = {
  background: 'linear-gradient(180deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%)'
};


// 使用业务逻辑 Composable
const {
  // 状态
  submitting,
  agreementChecked,
  showRiskNotification,
  activity,
  teamInfo,
  friendGroupInfo,
  couponInfo,
  selectedCouponId,
  isUsingPoints,
  teamPaymentMethod,
  playerProfileInfo,
  newPlayerProfile,
  feeInfo,
  userPointsInfo,
  activityFeeInfo,

  // 计算属性
  isReady,
  shouldShowPaymentOptions,
  canSubmit,
  feeDescriptionText,

  // 方法
  init,
  recalculateOrderPrice,
  handleSubmit,
  selectTeamPaymentMethod,
  handleTogglePoints,
  getPaymentTypeForFeeExplanation,
  handleAgreementChange,
  openAgreementModal,
  onReadRiskNotification,
  closeRiskNotification
} = useConfirmOrder(props);


// 处理优惠券选择
const handleCouponSelect = (couponId) => {
  console.log('[confirm-order] handleCouponSelect called with:', couponId);
  recalculateOrderPrice('coupon', couponId);
};



// 页面生命周期
onLoad(async () => {
  console.log('[confirm-order] onLoad called with props:', props);
  await init();
});
</script>

<style lang="scss" scoped>
.content-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 20rpx;
  padding-bottom: calc(180rpx + env(safe-area-inset-bottom));

  .bottom-spacer {
    height: 0;
  }
}

.bottom-bar-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  border-top: 1rpx solid #e8e8e8;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.no-team-info {
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  
  .section {
    margin-bottom: 20rpx;
  }

  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .empty-team-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    border: 1rpx solid #e8e8e8;
    border-radius: 16rpx;

    .empty-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}
</style>